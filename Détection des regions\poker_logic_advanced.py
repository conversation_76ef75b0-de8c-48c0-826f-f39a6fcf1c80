#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Logique avancée pour l'évaluation des mains de poker et le calcul des projections.
Ce module remplace la logique simpliste existante par des calculs précis et réalistes.
"""

class AdvancedPokerLogic:
    """Logique avancée pour l'évaluation des mains de poker"""

    def __init__(self):
        self.card_values = {
            # Français
            'As': 14, 'Roi': 13, 'Dame': 12, 'Valet': 11, '10': 10,
            '9': 9, '8': 8, '7': 7, '6': 6, '5': 5, '4': 4, '3': 3, '2': 2,
            # Ang<PERSON>s (pour compatibilité)
            'A': 14, 'K': 13, 'Q': 12, 'J': 11
        }
        self.value_names = {
            14: 'As', 13: 'Roi', 12: 'Dame', 11: 'Valet', 10: '10',
            9: '9', 8: '8', 7: '7', 6: '6', 5: '5', 4: '4', 3: '3', 2: '2'
        }

    def parse_cards(self, hand_cards, board_cards):
        """Parse les cartes en format numérique

        Args:
            hand_cards: Liste des cartes en main ["As de Cœur", "Roi de Pique"]
            board_cards: Liste des cartes du board

        Returns:
            tuple: (hand_values, hand_suits, board_values, board_suits, all_cards)
        """
        def parse_card_list(cards):
            values, suits = [], []
            for card in cards:
                if " de " in card:
                    value, suit = card.split(" de ")
                    if value in self.card_values:
                        values.append(self.card_values[value])
                        suits.append(suit)
                    else:
                        print(f"Valeur de carte non reconnue: '{value}' dans '{card}'")
            return values, suits

        hand_values, hand_suits = parse_card_list(hand_cards)
        board_values, board_suits = parse_card_list(board_cards)

        all_cards = list(zip(hand_values + board_values, hand_suits + board_suits))

        return hand_values, hand_suits, board_values, board_suits, all_cards

    def evaluate_made_hand(self, all_cards):
        """Évalue la main actuelle (sans projections)

        Returns:
            tuple: (rank, strength, description)
                rank: 0-9 (0=hauteur, 9=quinte flush royale)
                strength: valeur numérique pour comparer
                description: description textuelle
        """
        if len(all_cards) < 5:
            return self._evaluate_partial_hand(all_cards)

        values = [card[0] for card in all_cards]
        suits = [card[1] for card in all_cards]

        # Compter les occurrences
        value_counts = {}
        for value in values:
            value_counts[value] = value_counts.get(value, 0) + 1

        suit_counts = {}
        for suit in suits:
            suit_counts[suit] = suit_counts.get(suit, 0) + 1

        # Trier les valeurs uniques
        unique_values = sorted(set(values), reverse=True)

        # Vérifier les combinaisons dans l'ordre de force

        # 1. Quinte flush royale et quinte flush
        flush_suit = self._get_flush_suit(suit_counts)
        if flush_suit:
            straight_high = self._get_straight_high(unique_values, flush_suit, all_cards)
            if straight_high:
                if straight_high == 14:  # As high
                    return 9, 14, "Quinte flush royale"
                else:
                    return 8, straight_high, f"Quinte flush à {self.value_names[straight_high]}"

        # 2. Carré
        for value, count in value_counts.items():
            if count == 4:
                kicker = max([v for v in values if v != value])
                return 7, value * 100 + kicker, f"Carré de {self.value_names[value]}"

        # 3. Full house
        trips = [v for v, c in value_counts.items() if c == 3]
        pairs = [v for v, c in value_counts.items() if c == 2]

        if trips and pairs:
            trip_value = max(trips)
            pair_value = max(pairs)
            return 6, trip_value * 100 + pair_value, f"Full {self.value_names[trip_value]} par les {self.value_names[pair_value]}"
        elif len(trips) >= 2:  # Deux brelans
            trips.sort(reverse=True)
            return 6, trips[0] * 100 + trips[1], f"Full {self.value_names[trips[0]]} par les {self.value_names[trips[1]]}"

        # 4. Couleur
        if flush_suit:
            flush_values = [v for v, s in all_cards if s == flush_suit]
            flush_values.sort(reverse=True)
            strength = sum(flush_values[i] * (100 ** (4-i)) for i in range(5))
            return 5, strength, f"Couleur {flush_suit} par {self.value_names[flush_values[0]]}"

        # 5. Quinte
        straight_high = self._get_straight_high(unique_values)
        if straight_high:
            if straight_high == 5:  # Wheel (A-2-3-4-5)
                return 4, straight_high, "Quinte blanche (A-2-3-4-5)"
            elif straight_high == 14:  # Broadway (10-J-Q-K-A)
                return 4, straight_high, "Quinte broadway (10-J-Q-K-A)"
            else:
                return 4, straight_high, f"Quinte à {self.value_names[straight_high]}"

        # 6. Brelan
        if trips:
            trip_value = max(trips)
            kickers = sorted([v for v in values if v != trip_value], reverse=True)[:2]
            strength = trip_value * 10000 + kickers[0] * 100 + kickers[1]

            # Descriptions plus précises selon la force du brelan
            if trip_value >= 11:  # Brelan de figures
                return 3, strength, f"Brelan de {self.value_names[trip_value]} (fort)"
            elif trip_value >= 8:  # Brelan moyen
                return 3, strength, f"Brelan de {self.value_names[trip_value]} (moyen)"
            else:  # Brelan faible
                return 3, strength, f"Brelan de {self.value_names[trip_value]} (faible)"

        # 7. Deux paires
        if len(pairs) >= 2:
            pairs.sort(reverse=True)
            kicker = max([v for v in values if v not in pairs[:2]])
            strength = pairs[0] * 10000 + pairs[1] * 100 + kicker

            # Descriptions plus précises
            if pairs[0] >= 11 or pairs[1] >= 11:  # Au moins une paire de figures
                return 2, strength, f"Deux paires {self.value_names[pairs[0]]} et {self.value_names[pairs[1]]} (fortes)"
            elif pairs[0] >= 8:  # Paire haute moyenne
                return 2, strength, f"Deux paires {self.value_names[pairs[0]]} et {self.value_names[pairs[1]]} (moyennes)"
            else:
                return 2, strength, f"Deux paires {self.value_names[pairs[0]]} et {self.value_names[pairs[1]]} (faibles)"

        # 8. Paire
        if pairs:
            pair_value = max(pairs)
            kickers = sorted([v for v in values if v != pair_value], reverse=True)[:3]
            strength = pair_value * 1000000 + sum(kickers[i] * (100 ** (2-i)) for i in range(len(kickers)))

            # Descriptions plus précises selon la force de la paire
            if pair_value >= 13:  # Paire d'As ou de Rois
                return 1, strength, f"Paire de {self.value_names[pair_value]} (premium)"
            elif pair_value >= 11:  # Paire de figures
                return 1, strength, f"Paire de {self.value_names[pair_value]} (forte)"
            elif pair_value >= 8:  # Paire moyenne
                return 1, strength, f"Paire de {self.value_names[pair_value]} (moyenne)"
            else:  # Paire faible
                return 1, strength, f"Paire de {self.value_names[pair_value]} (faible)"

        # 9. Hauteur
        unique_values.sort(reverse=True)
        strength = sum(unique_values[i] * (100 ** (4-i)) for i in range(min(5, len(unique_values))))

        # Descriptions plus précises pour les hauteurs
        if unique_values[0] == 14:  # Hauteur As
            if len(unique_values) > 1 and unique_values[1] >= 12:  # As-Roi ou As-Dame
                return 0, strength, f"Hauteur {self.value_names[unique_values[0]]} (forte)"
            else:
                return 0, strength, f"Hauteur {self.value_names[unique_values[0]]}"
        elif unique_values[0] >= 12:  # Hauteur Roi ou Dame
            return 0, strength, f"Hauteur {self.value_names[unique_values[0]]} (moyenne)"
        else:  # Hauteur faible
            return 0, strength, f"Hauteur {self.value_names[unique_values[0]]} (faible)"

    def _get_flush_suit(self, suit_counts):
        """Retourne la couleur s'il y a une couleur (5+ cartes)"""
        for suit, count in suit_counts.items():
            if count >= 5:
                return suit
        return None

    def _get_straight_high(self, unique_values, flush_suit=None, all_cards=None):
        """Retourne la carte haute d'une quinte"""
        if len(unique_values) < 5:
            return None

        # Vérifier les quintes normales
        for i in range(len(unique_values) - 4):
            if unique_values[i] - unique_values[i+4] == 4:
                # Si on vérifie une quinte flush, s'assurer que toutes les cartes sont de la bonne couleur
                if flush_suit and all_cards:
                    straight_values = set(range(unique_values[i], unique_values[i]-5, -1))
                    flush_cards = {v for v, s in all_cards if s == flush_suit}
                    if not straight_values.issubset(flush_cards):
                        continue
                return unique_values[i]

        # Vérifier la quinte blanche (A-5-4-3-2)
        if 14 in unique_values and 5 in unique_values and 4 in unique_values and 3 in unique_values and 2 in unique_values:
            if flush_suit and all_cards:
                wheel_values = {14, 5, 4, 3, 2}
                flush_cards = {v for v, s in all_cards if s == flush_suit}
                if not wheel_values.issubset(flush_cards):
                    return None
            return 5  # Quinte blanche

        return None

    def _evaluate_partial_hand(self, all_cards):
        """Évalue une main avec moins de 5 cartes"""
        if not all_cards:
            return 0, 0, "Aucune carte"

        values = [card[0] for card in all_cards]
        value_counts = {}
        for value in values:
            value_counts[value] = value_counts.get(value, 0) + 1

        # Vérifier les paires/brelans
        pairs = [v for v, c in value_counts.items() if c == 2]
        trips = [v for v, c in value_counts.items() if c == 3]

        if trips:
            return 3, max(trips), f"Brelan de {self.value_names[max(trips)]}"
        elif pairs:
            return 1, max(pairs), f"Paire de {self.value_names[max(pairs)]}"
        else:
            return 0, max(values), f"Hauteur {self.value_names[max(values)]}"

    def calculate_draws_and_outs(self, hand_values, hand_suits, board_values, board_suits):
        """Calcule tous les tirages possibles et leurs outs

        Returns:
            dict: {
                'flush_draw': {'possible': bool, 'outs': int, 'description': str},
                'straight_draw': {'possible': bool, 'outs': int, 'description': str},
                'pair_draw': {'possible': bool, 'outs': int, 'description': str},
                'two_pair_draw': {'possible': bool, 'outs': int, 'description': str},
                'trips_draw': {'possible': bool, 'outs': int, 'description': str},
                'full_house_draw': {'possible': bool, 'outs': int, 'description': str},
                'quads_draw': {'possible': bool, 'outs': int, 'description': str},
                'total_outs': int,
                'clean_outs': int  # Outs qui ne donnent probablement pas une meilleure main à l'adversaire
            }
        """
        all_values = hand_values + board_values
        all_suits = hand_suits + board_suits
        all_cards = list(zip(all_values, all_suits))

        # Cartes déjà sorties
        used_cards = set(all_cards)

        # Compter les occurrences
        value_counts = {}
        for value in all_values:
            value_counts[value] = value_counts.get(value, 0) + 1

        suit_counts = {}
        for suit in all_suits:
            suit_counts[suit] = suit_counts.get(suit, 0) + 1

        draws = {
            'flush_draw': {'possible': False, 'outs': 0, 'description': ''},
            'straight_draw': {'possible': False, 'outs': 0, 'description': ''},
            'pair_draw': {'possible': False, 'outs': 0, 'description': ''},
            'two_pair_draw': {'possible': False, 'outs': 0, 'description': ''},
            'trips_draw': {'possible': False, 'outs': 0, 'description': ''},
            'full_house_draw': {'possible': False, 'outs': 0, 'description': ''},
            'quads_draw': {'possible': False, 'outs': 0, 'description': ''},
            'total_outs': 0,
            'clean_outs': 0
        }

        # 1. Tirage de couleur
        for suit, count in suit_counts.items():
            if count == 4:  # Besoin d'une carte pour la couleur
                remaining_cards = 13 - count  # 13 cartes de cette couleur au total
                # Soustraire les cartes de cette couleur déjà utilisées
                used_of_suit = sum(1 for v, s in used_cards if s == suit)
                outs = 13 - used_of_suit
                draws['flush_draw'] = {
                    'possible': True,
                    'outs': outs,
                    'description': f"Tirage de couleur {suit} ({outs} outs)"
                }
                break

        # 2. Tirage de quinte (seulement si on n'a pas déjà une quinte)
        current_hand_rank, _, _ = self.evaluate_made_hand(all_cards)
        if current_hand_rank < 4:  # Pas de quinte déjà formée
            straight_outs = self._calculate_straight_outs(all_values, used_cards)
            if straight_outs > 0:
                draws['straight_draw'] = {
                    'possible': True,
                    'outs': straight_outs,
                    'description': f"Tirage de quinte ({straight_outs} outs)"
                }

        # 3. Tirages de paires/brelans/carrés (seulement si on n'a pas déjà une main forte)
        pairs = [v for v, c in value_counts.items() if c == 2]
        trips = [v for v, c in value_counts.items() if c == 3]

        # Tirage de paire (si on n'a que des cartes isolées et pas de main forte déjà)
        if not pairs and not trips and len(hand_values) == 2 and current_hand_rank < 4:
            pair_outs = 0
            for value in hand_values:
                if value_counts[value] == 1:  # Carte isolée
                    # Soustraire celles déjà utilisées
                    used_of_value = sum(1 for v, s in used_cards if v == value)
                    pair_outs += max(0, 4 - used_of_value)

            if pair_outs > 0:
                draws['pair_draw'] = {
                    'possible': True,
                    'outs': pair_outs,
                    'description': f"Tirage de paire ({pair_outs} outs)"
                }

        # Tirage de deux paires (si on a une paire)
        if len(pairs) == 1 and len(hand_values) == 2:
            two_pair_outs = 0
            for value in hand_values:
                if value_counts[value] == 1:  # L'autre carte de la main
                    used_of_value = sum(1 for v, s in used_cards if v == value)
                    two_pair_outs += max(0, 3 - (used_of_value - 1))  # 3 cartes restantes

            if two_pair_outs > 0:
                draws['two_pair_draw'] = {
                    'possible': True,
                    'outs': two_pair_outs,
                    'description': f"Tirage de deux paires ({two_pair_outs} outs)"
                }

        # Tirage de brelan (si on a une paire)
        if pairs:
            trips_outs = 0
            for pair_value in pairs:
                used_of_value = sum(1 for v, s in used_cards if v == pair_value)
                trips_outs += max(0, 4 - used_of_value)  # Cartes restantes de cette valeur

            if trips_outs > 0:
                draws['trips_draw'] = {
                    'possible': True,
                    'outs': trips_outs,
                    'description': f"Tirage de brelan ({trips_outs} outs)"
                }

        # Tirage de full house (si on a un brelan)
        if trips:
            full_outs = 0
            # Outs pour transformer une paire en brelan
            for value, count in value_counts.items():
                if count == 2:  # Paire existante
                    used_of_value = sum(1 for v, s in used_cards if v == value)
                    full_outs += max(0, 4 - used_of_value)

            # Outs pour faire une nouvelle paire
            for value in range(2, 15):
                if value not in value_counts:  # Valeur pas encore présente
                    used_of_value = sum(1 for v, s in used_cards if v == value)
                    full_outs += max(0, min(2, 4 - used_of_value))  # Maximum 2 pour faire une paire

            if full_outs > 0:
                draws['full_house_draw'] = {
                    'possible': True,
                    'outs': full_outs,
                    'description': f"Tirage de full house ({full_outs} outs)"
                }

        # Tirage de carré (si on a un brelan)
        if trips:
            quads_outs = 0
            for trip_value in trips:
                used_of_value = sum(1 for v, s in used_cards if v == trip_value)
                quads_outs += max(0, 4 - used_of_value)

            if quads_outs > 0:
                draws['quads_draw'] = {
                    'possible': True,
                    'outs': quads_outs,
                    'description': f"Tirage de carré ({quads_outs} outs)"
                }

        # Calculer le total des outs (en évitant le double comptage)
        total_outs = 0
        clean_outs = 0

        # Prioriser les meilleurs tirages
        if draws['quads_draw']['possible']:
            total_outs += draws['quads_draw']['outs']
            clean_outs += draws['quads_draw']['outs']
        elif draws['full_house_draw']['possible']:
            total_outs += draws['full_house_draw']['outs']
            clean_outs += draws['full_house_draw']['outs']
        elif draws['flush_draw']['possible'] or draws['straight_draw']['possible']:
            # Éviter le double comptage entre couleur et quinte
            flush_outs = draws['flush_draw']['outs'] if draws['flush_draw']['possible'] else 0
            straight_outs = draws['straight_draw']['outs'] if draws['straight_draw']['possible'] else 0

            # Estimation conservative pour éviter le double comptage
            total_outs += max(flush_outs, straight_outs)
            clean_outs += max(flush_outs, straight_outs) * 0.8  # Réduction pour les outs "sales"
        else:
            # Additionner les autres tirages
            for draw_type in ['trips_draw', 'two_pair_draw', 'pair_draw']:
                if draws[draw_type]['possible']:
                    total_outs += draws[draw_type]['outs']
                    clean_outs += draws[draw_type]['outs'] * 0.9  # Légère réduction

        draws['total_outs'] = min(total_outs, 20)  # Limiter à 20 outs maximum
        draws['clean_outs'] = min(int(clean_outs), 15)  # Limiter à 15 outs propres

        return draws

    def _calculate_straight_outs(self, all_values, used_cards):
        """Calcule les outs pour les tirages de quinte"""
        unique_values = sorted(set(all_values), reverse=True)
        straight_outs = 0

        # Vérifier toutes les quintes possibles
        for high_card in range(14, 4, -1):  # As à 5
            straight_values = list(range(high_card, high_card-5, -1))

            # Compter combien de cartes de cette quinte on a
            cards_we_have = sum(1 for v in unique_values if v in straight_values)

            # Si on a 4 cartes, on a un tirage ouvert
            if cards_we_have == 4:
                missing_values = [v for v in straight_values if v not in unique_values]
                for missing_value in missing_values:
                    # Compter les cartes disponibles de cette valeur
                    used_of_value = sum(1 for v, s in used_cards if v == missing_value)
                    straight_outs += max(0, 4 - used_of_value)

        # Vérifier la quinte blanche (A-5-4-3-2)
        wheel_values = [14, 5, 4, 3, 2]
        cards_we_have_wheel = sum(1 for v in unique_values if v in wheel_values)

        if cards_we_have_wheel == 4:
            missing_values = [v for v in wheel_values if v not in unique_values]
            for missing_value in missing_values:
                used_of_value = sum(1 for v, s in used_cards if v == missing_value)
                straight_outs += max(0, 4 - used_of_value)

        return straight_outs

    def calculate_equity(self, hand_values, hand_suits, board_values, board_suits, num_opponents=1):
        """Calcule l'équité réaliste de la main - Version optimisée mémoire

        Args:
            num_opponents: Nombre d'adversaires (affecte l'équité)

        Returns:
            float: Équité estimée (0-100%)
        """
        try:
            # Calcul optimisé sans parsing complexe
            all_cards = list(zip(hand_values + board_values, hand_suits + board_suits))
            hand_rank, hand_strength, hand_desc = self.evaluate_made_hand(all_cards)

            # Équité de base selon la force de la main (optimisé)
            equity_map = [20, 30, 40, 50, 60, 70, 80, 85, 90, 95]
            base_equity = equity_map[min(hand_rank, 9)]

            # Ajustement rapide selon le nombre d'adversaires
            if num_opponents > 1:
                base_equity *= max(0.5, 1.0 - (num_opponents - 1) * 0.1)

            # Ajustement selon le stade de la partie (simplifié)
            board_size = len(board_values)
            if board_size == 0:  # Preflop
                if len(hand_values) == 2:
                    return self._calculate_preflop_equity_fast(hand_values, hand_suits, num_opponents)
            elif board_size <= 4:  # Flop/Turn - calcul rapide des tirages
                quick_outs = self._calculate_quick_outs(hand_values, hand_suits, board_values, board_suits)
                if quick_outs > 0:
                    cards_to_come = 5 - board_size
                    improvement_prob = min(quick_outs * 2 * cards_to_come, 40)
                    base_equity += improvement_prob * 0.5

            return min(max(base_equity, 5), 95)  # Limiter entre 5% et 95%

        except Exception:
            # En cas d'erreur, retourner une équité par défaut
            return 50.0
        finally:
            # Nettoyage mémoire
            try:
                import gc
                gc.collect()
            except:
                pass

    def _calculate_quick_outs(self, hand_values, hand_suits, board_values, board_suits):
        """Calcul rapide des outs principaux seulement"""
        try:
            outs = 0

            # Compter seulement les tirages de couleur évidents
            all_suits = hand_suits + board_suits
            suit_counts = {}
            for suit in all_suits:
                suit_counts[suit] = suit_counts.get(suit, 0) + 1

            # Tirage de couleur
            for count in suit_counts.values():
                if count == 4:  # Tirage de couleur
                    outs += 9
                    break

            # Estimation simple pour les autres tirages
            all_values = hand_values + board_values
            unique_values = len(set(all_values))
            if unique_values >= 3:
                outs += min(6, unique_values)  # Estimation très simple

            return min(outs, 15)  # Limiter à 15 outs max

        except Exception:
            return 0

    def _calculate_preflop_equity_fast(self, hand_values, hand_suits, num_opponents):
        """Calcul rapide de l'équité preflop"""
        try:
            if len(hand_values) != 2:
                return 20

            high_card, low_card = sorted(hand_values, reverse=True)
            suited = hand_suits[0] == hand_suits[1]

            # Calcul simplifié
            if high_card == low_card:  # Paire
                base_equity = 50 + (high_card - 2) * 3
            elif high_card == 14:  # As
                base_equity = 45 + (low_card - 2) * 2
            elif high_card >= 12:  # Roi ou Dame
                base_equity = 35 + (low_card - 2) * 1.5
            else:
                base_equity = 25 + (high_card - 2) * 1

            # Bonus pour les cartes assorties
            if suited and high_card != low_card:
                base_equity += 3

            # Ajustement selon le nombre d'adversaires
            if num_opponents > 1:
                base_equity *= max(0.5, 1.0 - (num_opponents - 1) * 0.15)

            return max(min(base_equity, 90), 15)

        except Exception:
            return 50.0

    def _calculate_preflop_equity(self, hand_values, hand_suits, num_opponents):
        """Calcule l'équité preflop basée sur des statistiques réelles"""
        if len(hand_values) != 2:
            return 20

        # Trier les cartes
        high_card, low_card = sorted(hand_values, reverse=True)
        suited = hand_suits[0] == hand_suits[1]

        # Équités approximatives contre 1 adversaire
        equity_table = {
            # Paires
            (14, 14): 85, (13, 13): 82, (12, 12): 80, (11, 11): 78, (10, 10): 75,
            (9, 9): 72, (8, 8): 69, (7, 7): 66, (6, 6): 63, (5, 5): 60,
            (4, 4): 57, (3, 3): 54, (2, 2): 51,

            # As + autre carte
            (14, 13): 67, (14, 12): 66, (14, 11): 65, (14, 10): 64, (14, 9): 62,
            (14, 8): 60, (14, 7): 59, (14, 6): 58, (14, 5): 59, (14, 4): 56,
            (14, 3): 55, (14, 2): 54,

            # Roi + autre carte
            (13, 12): 63, (13, 11): 62, (13, 10): 61, (13, 9): 58, (13, 8): 56,
            (13, 7): 54, (13, 6): 52, (13, 5): 51, (13, 4): 49, (13, 3): 48, (13, 2): 47,

            # Dame + autre carte
            (12, 11): 60, (12, 10): 58, (12, 9): 55, (12, 8): 53, (12, 7): 51,
            (12, 6): 49, (12, 5): 47, (12, 4): 46, (12, 3): 44, (12, 2): 43,

            # Valet + autre carte
            (11, 10): 56, (11, 9): 53, (11, 8): 50, (11, 7): 48, (11, 6): 46,
            (11, 5): 44, (11, 4): 42, (11, 3): 41, (11, 2): 39,
        }

        # Obtenir l'équité de base
        base_equity = equity_table.get((high_card, low_card), 35)

        # Bonus pour les cartes assorties
        if suited and high_card != low_card:
            base_equity += 3

        # Ajustement selon le nombre d'adversaires
        if num_opponents > 1:
            base_equity *= (1.0 - (num_opponents - 1) * 0.15)

        return max(min(base_equity, 90), 15)
