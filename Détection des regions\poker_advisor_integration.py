#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Wrapper d'intégration pour la logique avancée de poker.
Ce module fait le pont entre l'ancienne interface et la nouvelle logique.
"""

import re
from poker_logic_advanced import AdvancedPokerLogic

class PokerAdvisorIntegration:
    """Intégration de la logique avancée dans le conseiller poker"""

    def __init__(self):
        self.advanced_logic = AdvancedPokerLogic()

    def convert_card_format(self, card_value, card_suit):
        """Convertit le format des cartes du détecteur vers le format avancé

        Args:
            card_value: Valeur de la carte (A, K, Q, J, 10, 9, etc.)
            card_suit: Couleur de la carte (Cœur, Pique, <PERSON>rè<PERSON>, Carreau)

        Returns:
            str: Carte au format "As de Cœur"
        """
        # Mapping des valeurs
        value_mapping = {
            'A': 'As', 'K': 'Roi', 'Q': 'Dame', 'J': 'Valet',
            '10': '10', '9': '9', '8': '8', '7': '7', '6': '6',
            '5': '5', '4': '4', '3': '3', '2': '2'
        }

        french_value = value_mapping.get(card_value, card_value)
        return f"{french_value} de {card_suit}"

    def evaluate_hand_advanced(self, hand_values, hand_suits, board_values, board_suits, game_info=None):
        """Évalue une main avec la logique avancée et les informations de jeu

        Args:
            hand_values: Liste des valeurs des cartes en main
            hand_suits: Liste des couleurs des cartes en main
            board_values: Liste des valeurs des cartes du board
            board_suits: Liste des couleurs des cartes du board
            game_info: Dictionnaire avec les informations de jeu (mises, jetons, etc.)

        Returns:
            dict: {
                'hand_rank': int,
                'hand_strength': int,
                'hand_description': str,
                'draws': dict,
                'equity': float,
                'recommendations': dict,
                'game_analysis': dict
            }
        """
        # Protection ultra-renforcée (logs de debug désactivés pour les performances)
        try:
            # Validation des paramètres d'entrée
            if not isinstance(hand_values, list) or not isinstance(hand_suits, list):
                return self._get_default_result("Paramètres main invalides")

            if not isinstance(board_values, list) or not isinstance(board_suits, list):
                return self._get_default_result("Paramètres board invalides")

            if len(hand_values) != len(hand_suits):
                return self._get_default_result("Longueurs main incohérentes")

            if len(board_values) != len(board_suits):
                return self._get_default_result("Longueurs board incohérentes")

            # Convertir au format français
            hand_cards = []
            for i, value in enumerate(hand_values):
                if i < len(hand_suits):
                    hand_cards.append(self.convert_card_format(value, hand_suits[i]))

            board_cards = []
            for i, value in enumerate(board_values):
                if i < len(board_suits):
                    board_cards.append(self.convert_card_format(value, board_suits[i]))

            # Parser les cartes
            h_vals, h_suits, b_vals, b_suits, all_cards = self.advanced_logic.parse_cards(
                hand_cards, board_cards
            )

            # Évaluer la main en s'assurant qu'on utilise les cartes en main du joueur
            hand_rank, hand_strength, hand_description = self.evaluate_player_hand(
                all_cards, list(zip(h_vals, h_suits))
            )

            # VÉRIFIER D'ABORD LES MAINS FAITES AVANT LES TIRAGES
            draws = self.calculate_draws_with_made_hands_check(h_vals, h_suits, b_vals, b_suits, hand_rank)

            # Calculer l'équité
            equity = self.advanced_logic.calculate_equity(h_vals, h_suits, b_vals, b_suits)

            # Analyser les informations de jeu
            game_analysis = self.analyze_game_info(game_info) if game_info else {}

            # Générer des recommandations avec les informations de jeu
            recommendations = self.generate_recommendations_with_game_info(
                hand_rank, hand_description, draws, equity, len(board_cards), game_analysis
            )

            return {
                'hand_rank': hand_rank,
                'hand_strength': hand_strength,
                'hand_description': hand_description,
                'draws': draws,
                'equity': equity,
                'recommendations': recommendations,
                'game_analysis': game_analysis
            }

        except Exception as e:
            print(f"❌ Erreur dans l'analyse avancée: {e}")
            return self._get_default_result(f"Erreur: {e}")
        finally:
            # Nettoyage mémoire explicite pour éviter les fuites
            try:
                import gc
                gc.collect()  # Force le garbage collector
            except:
                pass

    def _get_default_result(self, error_message):
        """Retourne un résultat par défaut en cas d'erreur"""
        return {
            'hand_rank': 0,
            'hand_strength': 0,
            'hand_description': 'Erreur d\'analyse',
            'draws': {
                'flush_draw': {'possible': False, 'outs': 0, 'description': ''},
                'straight_draw': {'possible': False, 'outs': 0, 'description': ''},
                'pair_draw': {'possible': False, 'outs': 0, 'description': ''},
                'total_outs': 0,
                'clean_outs': 0
            },
            'equity': 50.0,
            'recommendations': {
                'action': 'check',
                'reason': error_message,
                'aggression_level': 'passive',
                'bet_sizing': 'check'
            }
        }

    def evaluate_player_hand(self, all_cards, hand_cards):
        """Évalue la meilleure main du joueur en s'assurant qu'au moins une carte en main est utilisée

        Args:
            all_cards: Toutes les cartes disponibles (main + board)
            hand_cards: Cartes en main du joueur

        Returns:
            tuple: (rank, strength, description)
        """
        if len(all_cards) < 5:
            return self.advanced_logic._evaluate_partial_hand(all_cards)

        # Si on a exactement 5 cartes ou moins, évaluer directement
        if len(all_cards) <= 5:
            return self.advanced_logic.evaluate_made_hand(all_cards)

        # Si on n'a pas de cartes en main spécifiées, utiliser l'évaluation standard
        if not hand_cards:
            return self.advanced_logic.evaluate_made_hand(all_cards)

        # Générer toutes les combinaisons de 5 cartes qui incluent au moins une carte en main
        from itertools import combinations

        # Séparer les cartes en main et les cartes du board
        board_cards = [card for card in all_cards if card not in hand_cards]

        best_rank = -1
        best_strength = 0
        best_description = ""

        # Essayer toutes les combinaisons possibles avec au moins une carte en main
        for num_hand_cards in range(1, min(len(hand_cards) + 1, 6)):  # 1 à 2 cartes en main max
            if num_hand_cards > len(hand_cards):
                continue

            remaining_slots = 5 - num_hand_cards
            if remaining_slots > len(board_cards):
                continue

            # Combinaisons de cartes en main
            for hand_combo in combinations(hand_cards, num_hand_cards):
                # Combinaisons de cartes du board
                for board_combo in combinations(board_cards, remaining_slots):
                    five_card_hand = list(hand_combo) + list(board_combo)

                    if len(five_card_hand) == 5:
                        rank, strength, description = self.advanced_logic.evaluate_made_hand(five_card_hand)

                        if rank > best_rank or (rank == best_rank and strength > best_strength):
                            best_rank = rank
                            best_strength = strength
                            best_description = description

        # Si aucune combinaison trouvée, utiliser l'évaluation standard
        if best_rank == -1:
            return self.advanced_logic.evaluate_made_hand(all_cards)

        return best_rank, best_strength, best_description

    def calculate_draws_with_made_hands_check(self, hand_values, hand_suits, board_values, board_suits, hand_rank):
        """Calcule les tirages en vérifiant d'abord si des mains sont déjà faites"""

        # Combiner toutes les cartes
        all_values = hand_values + board_values
        all_suits = hand_suits + board_suits

        print(f"🔍 Vérification des mains faites:")
        print(f"   Main: {hand_values} {hand_suits}")
        print(f"   Board: {board_values} {board_suits}")
        print(f"   Rang détecté: {hand_rank}")

        # Initialiser la structure des tirages
        draws = {
            'flush_draw': {'possible': False, 'outs': 0, 'description': ''},
            'straight_draw': {'possible': False, 'outs': 0, 'description': ''},
            'pair_draw': {'possible': False, 'outs': 0, 'description': ''},
            'trips_draw': {'possible': False, 'outs': 0, 'description': ''},
            'two_pair_draw': {'possible': False, 'outs': 0, 'description': ''},
            'total_outs': 0,
            'clean_outs': 0
        }

        # VÉRIFIER LES MAINS DÉJÀ FAITES

        # 1. Vérifier la couleur faite
        suit_counts = {}
        for suit in all_suits:
            suit_counts[suit] = suit_counts.get(suit, 0) + 1

        max_suit_count = max(suit_counts.values()) if suit_counts else 0
        if max_suit_count >= 5:
            # COULEUR FAITE !
            flush_suit = [suit for suit, count in suit_counts.items() if count >= 5][0]
            draws['flush_draw'] = {
                'possible': True,
                'outs': 0,  # 0 outs car déjà faite
                'description': f'COULEUR FAITE ({flush_suit})'
            }
            print(f"   ✅ COULEUR FAITE détectée: {flush_suit} ({max_suit_count} cartes)")

        # 2. Vérifier la quinte faite
        # Convertir les valeurs en nombres pour vérifier les séquences
        value_map = {
            'As': [1, 14], '2': [2], '3': [3], '4': [4], '5': [5], '6': [6], '7': [7],
            '8': [8], '9': [9], '10': [10], 'Valet': [11], 'Dame': [12], 'Roi': [13]
        }

        numeric_values = []
        for val in all_values:
            if val in value_map:
                numeric_values.extend(value_map[val])

        unique_values = sorted(set(numeric_values))

        # Vérifier les séquences de 5 cartes consécutives
        straight_found = False

        # Vérifier d'abord la wheel spéciale (A-2-3-4-5)
        wheel_values = [1, 2, 3, 4, 5]
        if all(val in unique_values for val in wheel_values):
            straight_found = True
            draws['straight_draw'] = {
                'possible': True,
                'outs': 0,
                'description': "QUINTE FAITE (A-2-3-4-5, wheel)"
            }
            print(f"   ✅ QUINTE FAITE détectée: A-2-3-4-5 (wheel)")

        # Vérifier les autres quintes si pas de wheel
        if not straight_found:
            for i in range(len(unique_values) - 4):
                if unique_values[i+4] - unique_values[i] == 4 and unique_values[i] > 1:  # Exclure la wheel déjà vérifiée
                    straight_found = True
                    straight_high = unique_values[i+4]
                    if straight_high == 14:
                        straight_desc = "QUINTE FAITE (10-A, broadway)"
                    else:
                        straight_desc = f"QUINTE FAITE (jusqu'au {straight_high})"

                    draws['straight_draw'] = {
                        'possible': True,
                        'outs': 0,
                        'description': straight_desc
                    }
                    print(f"   ✅ QUINTE FAITE détectée: {straight_desc}")
                    break

        # Si pas de quinte faite, vérifier les tirages de quinte
        if not straight_found:
            # Vérifier tirage de wheel (A-2-3-4-5)
            wheel_present = sum(1 for val in wheel_values if val in unique_values)
            if wheel_present == 4:  # Il manque 1 carte pour la wheel
                missing_wheel = [val for val in wheel_values if val not in unique_values][0]
                draws['straight_draw'] = {
                    'possible': True,
                    'outs': 4,
                    'description': f"Tirage quinte wheel (manque {missing_wheel})"
                }
                print(f"   🎯 Tirage quinte wheel détecté (manque {missing_wheel})")
                straight_found = True  # Pour éviter le double calcul

        # Si aucune main faite, calculer les vrais tirages
        if not straight_found and max_suit_count < 5:
            print("   🔍 Aucune main faite, calcul des tirages...")
            draws = self.advanced_logic.calculate_draws_and_outs(hand_values, hand_suits, board_values, board_suits)

        return draws

    def analyze_game_info(self, game_info):
        """Analyse les informations de jeu détectées"""
        analysis = {
            'detected_regions': [],
            'player_stacks': {},
            'player_bets': {},
            'pot_size': 0,
            'my_stack': 0,
            'my_bet': 0,
            'button_position': None,
            'stack_depth': 'unknown',
            'action_required': 0,
            'pot_odds': 0
        }

        if not game_info:
            return analysis

        # Extraire les informations détectées (TABLE DE 6 JOUEURS MAX)
        for region_name, region_data in game_info.items():
            if not region_data or not region_data.get('text'):
                continue

            text = region_data['text'].strip()
            if not text:
                continue

            analysis['detected_regions'].append(region_name)

            # Analyser les jetons des joueurs (SEULEMENT JOUEURS 1-6)
            if 'jetons_joueur' in region_name:
                player_num = region_name.replace('jetons_joueur', '')
                if player_num.isdigit() and 1 <= int(player_num) <= 6:  # Table de 6 max
                    try:
                        stack = float(text.replace(',', '.'))
                        analysis['player_stacks'][f'joueur{player_num}'] = stack
                        print(f"💰 Joueur {player_num} - Jetons: {stack} BB")
                    except:
                        pass

            # Analyser les mises des joueurs (SEULEMENT JOUEURS 1-6)
            elif 'mise_joueur' in region_name:
                player_num = region_name.replace('mise_joueur', '')
                if player_num.isdigit() and 1 <= int(player_num) <= 6:  # Table de 6 max
                    try:
                        bet = float(text.replace(',', '.'))
                        analysis['player_bets'][f'joueur{player_num}'] = bet
                        print(f"🎯 Joueur {player_num} - Mise: {bet} BB")
                    except:
                        pass

            # Analyser mes informations
            elif region_name == 'mes_jetons':
                try:
                    analysis['my_stack'] = float(text.replace(',', '.'))
                except:
                    pass

            elif region_name == 'ma_mise':
                try:
                    analysis['my_bet'] = float(text.replace(',', '.'))
                except:
                    pass

            # Analyser le pot
            elif region_name in ['pot', 'pot_total']:
                try:
                    analysis['pot_size'] = float(text.replace(',', '.'))
                except:
                    pass

            # Analyser les montants d'action
            elif region_name == 'montant_call':
                try:
                    analysis['action_required'] = float(text.replace(',', '.'))
                except:
                    pass

        # Détecter le bouton (2 positions avant la grosse blinde) - TABLE DE 6 JOUEURS
        if analysis['player_bets']:
            max_bet = max(analysis['player_bets'].values())

            # Trouver la grosse blinde (celui avec la plus grosse mise)
            big_blind_player = None
            for player, bet in analysis['player_bets'].items():
                if bet == max_bet:
                    big_blind_player = player
                    break

            if big_blind_player:
                # Extraire le numéro du joueur de la grosse blinde
                bb_match = re.search(r'joueur(\d+)', big_blind_player)
                if bb_match:
                    bb_num = int(bb_match.group(1))

                    # Calculer la position du bouton (2 positions avant la grosse blinde)
                    # En table de 6, les positions sont 1,2,3,4,5,6 puis retour à 1
                    button_num = bb_num - 2
                    if button_num <= 0:
                        button_num += 6  # Faire le tour de la table

                    button_player = f"joueur{button_num}"
                    analysis['button_position'] = button_player
                    analysis['big_blind_position'] = big_blind_player

                    print(f"🎯 Grosse blinde détectée: {big_blind_player} (mise: {max_bet} BB)")
                    print(f"🔘 Bouton calculé: {button_player} (2 positions avant la grosse blinde)")

                    # Afficher toutes les mises avec les positions
                    print("📊 Mises détectées avec positions:")
                    for player, bet in sorted(analysis['player_bets'].items()):
                        markers = []
                        if player == analysis['button_position']:
                            markers.append("🔘 BTN")
                        if player == big_blind_player:
                            markers.append("🎯 BB")
                        marker_str = f" ({', '.join(markers)})" if markers else ""
                        print(f"   {player}: {bet} BB{marker_str}")

        # Calculer la profondeur des tapis (TABLE DE 6 JOUEURS) - BASÉE SUR LA MOYENNE
        all_stacks = list(analysis['player_stacks'].values())
        if analysis['my_stack'] > 0:
            all_stacks.append(analysis['my_stack'])

        if all_stacks:
            avg_stack = sum(all_stacks) / len(all_stacks)
            min_stack = min(all_stacks)
            max_stack = max(all_stacks)

            print(f"💰 Analyse des tapis (table de 6):")
            print(f"   Nombre de joueurs détectés: {len(all_stacks)}")
            print(f"   Mes jetons: {analysis['my_stack']:.1f} BB")
            print(f"   Tapis moyen de la table: {avg_stack:.1f} BB")
            print(f"   Tapis minimum: {min_stack:.1f} BB")
            print(f"   Tapis maximum: {max_stack:.1f} BB")

            # Classification basée sur la MOYENNE des tapis (pas le minimum)
            if avg_stack < 20:  # Moins de 20 BB en moyenne
                analysis['stack_depth'] = 'court'
            elif avg_stack < 50:  # Entre 20 et 50 BB en moyenne
                analysis['stack_depth'] = 'moyen'
            else:  # Plus de 50 BB en moyenne
                analysis['stack_depth'] = 'profond'

            # Stocker les valeurs pour l'affichage
            analysis['average_stack'] = avg_stack
            analysis['min_stack'] = min_stack
            analysis['max_stack'] = max_stack

            print(f"   Classification: Tapis {analysis['stack_depth']} (basé sur moyenne: {avg_stack:.1f} BB)")

        # Calculer les pot odds
        if analysis['action_required'] > 0 and analysis['pot_size'] > 0:
            analysis['pot_odds'] = (analysis['action_required'] / (analysis['pot_size'] + analysis['action_required'])) * 100

        return analysis

    def generate_recommendations_with_game_info(self, hand_rank, hand_desc, draws, equity, board_stage, game_analysis):
        """Génère des recommandations ULTRA-AVANCÉES en tenant compte de TOUTES les informations de jeu"""
        # Commencer par les recommandations de base
        recommendations = self.generate_recommendations(hand_rank, hand_desc, draws, equity, board_stage)

        # Analyser TOUTES les informations de jeu disponibles
        if game_analysis:
            # 1. ANALYSE DE LA PROFONDEUR DES TAPIS (basée sur la moyenne)
            stack_depth = game_analysis.get('stack_depth', 'unknown')
            average_stack = game_analysis.get('average_stack', 0)
            min_stack = game_analysis.get('min_stack', 0)

            print(f"🎯 Ajustements selon les tapis:")
            print(f"   Profondeur: {stack_depth} (moyenne: {average_stack:.1f} BB)")
            print(f"   Tapis effectif: {min_stack:.1f} BB")

            if stack_depth == 'court':
                # Tapis courts : stratégie push/fold plus agressive
                if hand_rank >= 1:  # Paire ou mieux
                    if average_stack < 15:  # Très court
                        recommendations['action'] = 'all-in'
                        recommendations['reason'] = f"{hand_desc} - Tapis très court ({average_stack:.1f} BB), push/fold"
                        recommendations['bet_sizing'] = 'all-in'
                    else:
                        recommendations['action'] = 'raise/call'
                        recommendations['reason'] += f" (Tapis court {average_stack:.1f} BB)"
                        recommendations['bet_sizing'] = '2/3 pot'
                elif equity > 40:  # Bon tirage avec tapis court
                    recommendations['action'] = 'call/raise'
                    recommendations['reason'] = f"Bon tirage avec tapis court ({average_stack:.1f} BB)"

            elif stack_depth == 'profond':
                # Tapis profonds : jouer plus prudemment, extraire plus de valeur
                if hand_rank >= 5:  # Couleur ou mieux
                    recommendations['action'] = 'bet/raise'
                    recommendations['reason'] = f"{hand_desc} - Tapis profond ({average_stack:.1f} BB), extraire valeur"
                    recommendations['bet_sizing'] = '3/4 pot'
                elif hand_rank < 2:  # Moins que deux paires
                    recommendations['aggression_level'] = 'passive'
                    recommendations['reason'] += f" (Tapis profond {average_stack:.1f} BB, prudence)"

            # 2. ANALYSE DES POT ODDS ET ÉQUITÉ
            pot_odds = game_analysis.get('pot_odds', 0)
            if pot_odds > 0:
                print(f"🎯 Analyse pot odds: {pot_odds:.1f}% vs équité {equity:.1f}%")

                if equity > pot_odds + 10:  # Marge de sécurité
                    recommendations['action'] = 'call/raise'
                    recommendations['reason'] = f"Équité {equity:.1f}% >> Pot odds {pot_odds:.1f}% (profitable)"
                elif equity > pot_odds:
                    recommendations['action'] = 'call'
                    recommendations['reason'] = f"Équité {equity:.1f}% > Pot odds {pot_odds:.1f}% (call marginal)"
                else:
                    if hand_rank < 1:  # Pas de paire
                        recommendations['action'] = 'fold'
                        recommendations['reason'] = f"Équité {equity:.1f}% < Pot odds {pot_odds:.1f}% (fold)"

            # 3. ANALYSE DE POSITION (basée sur le bouton détecté)
            button_pos = game_analysis.get('button_position')
            if button_pos:
                my_position = self._determine_my_position(button_pos, game_analysis)
                print(f"🎯 Position détectée: {my_position}")

                if my_position in ['button', 'cutoff']:  # Position tardive
                    if hand_rank >= 1 or equity > 35:
                        recommendations['aggression_level'] = 'aggressive'
                        recommendations['reason'] += f" (Position tardive: {my_position})"
                elif my_position in ['early', 'middle']:  # Position précoce
                    if hand_rank < 2:  # Moins que deux paires
                        recommendations['aggression_level'] = 'passive'
                        recommendations['reason'] += f" (Position précoce: {my_position})"

            # 4. ANALYSE DU NOMBRE D'ADVERSAIRES
            num_opponents = len(game_analysis.get('player_stacks', {}))
            if num_opponents > 0:
                print(f"🎯 Nombre d'adversaires: {num_opponents}")

                if num_opponents >= 4:  # Multiway
                    if hand_rank < 3:  # Moins qu'un brelan
                        recommendations['aggression_level'] = 'passive'
                        recommendations['reason'] += f" (Multiway {num_opponents} joueurs)"
                elif num_opponents == 1:  # Heads-up
                    if hand_rank >= 1 or equity > 40:
                        recommendations['aggression_level'] = 'aggressive'
                        recommendations['reason'] += " (Heads-up)"

            # 5. DÉTECTION DES MAINS FAITES vs TIRAGES
            flush_made = any('COULEUR FAITE' in str(draw.get('description', '')) for draw in draws.values() if isinstance(draw, dict))
            straight_made = any('QUINTE FAITE' in str(draw.get('description', '')) for draw in draws.values() if isinstance(draw, dict))

            if flush_made or straight_made:
                print(f"🎯 Main faite détectée: Couleur={flush_made}, Quinte={straight_made}")
                recommendations['action'] = 'bet/raise'
                recommendations['reason'] = f"Main faite - {hand_desc}"
                recommendations['aggression_level'] = 'aggressive'
                recommendations['bet_sizing'] = '2/3 pot'

        return recommendations

    def _determine_my_position(self, button_position, game_analysis):
        """Détermine ma position relative au bouton"""
        if not button_position:
            return 'unknown'

        # Extraire le numéro du bouton
        button_match = re.search(r'joueur(\d+)', button_position)
        if not button_match:
            return 'unknown'

        button_num = int(button_match.group(1))

        # En table de 6, déterminer les positions
        # Supposons que je suis toujours "joueur0" ou que ma position est déterminée autrement
        # Pour l'instant, retourner une position basée sur le bouton
        positions = ['early', 'early', 'middle', 'middle', 'cutoff', 'button']
        return positions[(button_num - 1) % 6]

    def generate_recommendations(self, hand_rank, hand_desc, draws, equity, board_stage):
        """Génère des recommandations d'action intelligentes"""
        recommendations = {
            'action': 'check',
            'reason': 'Action par défaut',
            'aggression_level': 'passive',
            'bet_sizing': '1/3 pot'
        }

        # Recommandations selon la force de la main
        if hand_rank >= 6:  # Full house ou mieux
            recommendations.update({
                'action': 'bet/raise',
                'reason': f'{hand_desc} - Main très forte, extraire de la valeur',
                'aggression_level': 'aggressive',
                'bet_sizing': '2/3 pot'
            })
        elif hand_rank >= 4:  # Quinte ou couleur
            recommendations.update({
                'action': 'bet/call',
                'reason': f'{hand_desc} - Main forte, jouer pour la valeur',
                'aggression_level': 'moderate',
                'bet_sizing': '1/2 pot'
            })
        elif hand_rank >= 2:  # Deux paires ou brelan
            recommendations.update({
                'action': 'bet/call',
                'reason': f'{hand_desc} - Main correcte, jouer prudemment',
                'aggression_level': 'moderate',
                'bet_sizing': '1/3 pot'
            })
        elif hand_rank == 1:  # Paire
            if equity > 50:
                recommendations.update({
                    'action': 'bet/call',
                    'reason': f'{hand_desc} - Paire avec bonne équité',
                    'aggression_level': 'moderate',
                    'bet_sizing': '1/3 pot'
                })
            else:
                recommendations.update({
                    'action': 'check/call',
                    'reason': f'{hand_desc} - Paire faible, jouer défensivement',
                    'aggression_level': 'passive',
                    'bet_sizing': 'check'
                })
        else:  # Hauteur
            # Vérifier les tirages
            total_outs = draws.get('total_outs', 0)
            if total_outs >= 8:  # Bon tirage
                recommendations.update({
                    'action': 'bet/call',
                    'reason': f'Hauteur avec {total_outs} outs - Bon tirage',
                    'aggression_level': 'moderate',
                    'bet_sizing': '1/3 pot'
                })
            elif total_outs >= 4:  # Tirage moyen
                recommendations.update({
                    'action': 'check/call',
                    'reason': f'Hauteur avec {total_outs} outs - Tirage moyen',
                    'aggression_level': 'passive',
                    'bet_sizing': 'check'
                })
            else:  # Pas de tirage
                if equity < 25:
                    recommendations.update({
                        'action': 'fold',
                        'reason': 'Hauteur sans tirage - Main trop faible',
                        'aggression_level': 'fold',
                        'bet_sizing': 'fold'
                    })
                else:
                    recommendations.update({
                        'action': 'check',
                        'reason': 'Hauteur sans tirage - Contrôler le pot',
                        'aggression_level': 'passive',
                        'bet_sizing': 'check'
                    })

        # Ajustements selon le stade de la partie
        if board_stage == 0:  # Preflop
            if equity > 70:
                recommendations['action'] = 'raise'
                recommendations['reason'] = 'Main premium preflop'
            elif equity > 50:
                recommendations['action'] = 'call/raise'
                recommendations['reason'] = 'Main jouable preflop'
            elif equity < 30:
                recommendations['action'] = 'fold'
                recommendations['reason'] = 'Main trop faible preflop'

        return recommendations

    def format_analysis_text(self, analysis_result):
        """Formate le résultat d'analyse pour l'affichage"""
        hand_desc = analysis_result['hand_description']
        draws = analysis_result['draws']
        equity = analysis_result['equity']
        recommendations = analysis_result['recommendations']

        # Construire la description des tirages
        draw_descriptions = []
        for draw_type, draw_info in draws.items():
            if draw_type not in ['total_outs', 'clean_outs'] and draw_info.get('possible', False):
                draw_descriptions.append(draw_info['description'])

        # Texte principal
        analysis_text = f"Main actuelle: {hand_desc}\n"
        analysis_text += f"Équité estimée: {equity:.1f}%\n"

        if draw_descriptions:
            analysis_text += f"Tirages: {', '.join(draw_descriptions)}\n"

        analysis_text += f"\nRecommandation: {recommendations['action'].upper()}\n"
        analysis_text += f"Raison: {recommendations['reason']}"

        return analysis_text

# Instance globale pour l'intégration
poker_integration = PokerAdvisorIntegration()
