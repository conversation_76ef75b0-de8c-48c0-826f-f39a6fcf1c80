#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Poker Advisor Light - Module de conseiller poker léger et rapide
================================================================

Version simplifiée du conseiller poker pour une intégration directe
dans l'interface de détection des régions.

Ce module permet d'analyser rapidement les cartes détectées et de
fournir des recommandations stratégiques optimales.

Auteur: Augment Agent
Date: 2023-2025
"""

import re
import time
from collections import OrderedDict

# Constantes pour les cartes
CARD_VALUES = ["A", "K", "Q", "J", "10", "9", "8", "7", "6", "5", "4", "3", "2"]
CARD_SUITS = ["Cœur", "<PERSON><PERSON>", "Trè<PERSON>", "<PERSON><PERSON>"]

# Constantes pour les couleurs d'affichage
CARD_COLORS = {
    "Cœur": "rouge",
    "Pique": "noir",
    "Trèfle": "vert",
    "Carreau": "bleu",
    "Valeur": "blanc"
}

# Mappings pour corriger les erreurs de détection courantes
CARD_VALUE_CORRECTIONS = {
    # Confusion 9/6 - priorité au 9 pour g/G, priorité au 6 pour b/B
    "g": "9", "G": "9", "q": "9", "Q": "9",
    "b": "6", "B": "6", "p": "6", "P": "6",
    # Confusion 8/B
    "B": "8", "b": "8",
    # Confusion 7/T/L/I
    "T": "7", "l": "7", "L": "7", "I": "7", "i": "7",
    # Confusion 5/S/G
    "S": "5", "s": "5",
    # Confusion A/4
    "A": "4", "a": "4",
    # Confusion 3/E
    "E": "3", "e": "3",
    # Confusion 2/Z
    "Z": "2", "z": "2"
}

class PokerAdvisorLight:
    """Classe principale du conseiller poker léger"""

    def __init__(self, cache_size=20):
        """
        Initialise le conseiller poker

        Args:
            cache_size (int): Taille du cache pour les analyses
        """
        self.cache = PokerDataCache(max_size=cache_size)
        self.last_analysis = None
        self.last_data = None
        self.manual_corrections = {}  # Dictionnaire pour stocker les corrections manuelles

    def set_manual_correction(self, region_name, corrected_value, corrected_suit=None):
        """
        Définit une correction manuelle pour une carte

        Args:
            region_name (str): Nom de la région (ex: "card_1", "hand_card_1")
            corrected_value (str): Valeur corrigée de la carte (ex: "A", "K", "Q", etc.) ou "" pour "pas de cartes"
            corrected_suit (str, optional): Couleur corrigée de la carte (ex: "Cœur", "Pique", etc.)

        Returns:
            bool: True si la correction a été appliquée avec succès
        """
        # Valider la valeur de la carte (peut être vide pour "pas de cartes")
        if corrected_value not in CARD_VALUES and corrected_value != "":
            return False

        # Valider la couleur de la carte (peut être vide)
        if corrected_suit is not None and corrected_suit not in CARD_SUITS and corrected_suit != "":
            return False

        # Stocker la correction (même si la valeur est vide pour "pas de cartes")
        self.manual_corrections[region_name] = {
            "value": corrected_value,
            "suit": corrected_suit if corrected_suit else ""
        }

        # Vider le cache pour forcer une nouvelle analyse
        self.cache.clear()

        return True

    def remove_manual_correction(self, region_name):
        """
        Supprime une correction manuelle pour une région

        Args:
            region_name (str): Nom de la région

        Returns:
            bool: True si la correction a été supprimée
        """
        if region_name in self.manual_corrections:
            del self.manual_corrections[region_name]
            self.cache.clear()
            return True
        return False

    def get_manual_corrections(self):
        """
        Récupère toutes les corrections manuelles

        Returns:
            dict: Dictionnaire des corrections manuelles
        """
        return self.manual_corrections

    def clear_manual_corrections(self):
        """
        Efface toutes les corrections manuelles

        Returns:
            int: Nombre de corrections effacées
        """
        count = len(self.manual_corrections)
        self.manual_corrections.clear()
        self.cache.clear()
        return count

    def analyze_detection_results(self, results):
        """
        Analyse les résultats de détection et fournit des recommandations

        Args:
            results (dict): Résultats de la détection des cartes

        Returns:
            dict: Analyse complète avec recommandations
        """
        # Extraire les données de poker des résultats
        data = self.extract_poker_data(results)

        # Vérifier si de nouvelles cartes ont été détectées et supprimer les anciennes corrections si nécessaire
        self._check_and_update_cards(data, results)

        # Vérifier si les données sont dans le cache
        analysis, formatted_analysis, from_cache = self.cache.get(data)
        if from_cache:
            self.last_analysis = analysis
            self.last_data = data
            return analysis, formatted_analysis

        # Analyser la situation de poker
        analysis = self.analyze_poker_situation(data)

        # Formater l'analyse pour l'affichage
        formatted_analysis = self.format_analysis(data, analysis)

        # Mettre en cache les résultats
        self.cache.put(data, analysis, formatted_analysis)

        # Stocker les dernières données et analyse
        self.last_analysis = analysis
        self.last_data = data

        return analysis, formatted_analysis

    def _check_and_update_cards(self, current_data, results):
        """
        Vérifie si de nouvelles cartes ont été détectées et supprime les anciennes corrections si nécessaire

        Args:
            current_data (dict): Données de poker actuelles
            results (dict): Résultats de la détection
        """
        # Si c'est la première analyse, ne rien faire
        if self.last_data is None:
            return

        # Extraire les cartes actuelles et précédentes
        current_board_cards = current_data.get("board_cards_text", "").split(", ") if current_data.get("board_cards_text") else []
        current_hand_cards = current_data.get("hand_cards_text", "").split(", ") if current_data.get("hand_cards_text") else []

        previous_board_cards = self.last_data.get("board_cards_text", "").split(", ") if self.last_data.get("board_cards_text") else []
        previous_hand_cards = self.last_data.get("hand_cards_text", "").split(", ") if self.last_data.get("hand_cards_text") else []

        # Si le nombre de cartes a changé, vérifier les corrections à supprimer
        if len(current_board_cards) != len(previous_board_cards) or len(current_hand_cards) != len(previous_hand_cards):
            # Vérifier les régions qui ont changé
            regions_to_check = []

            # Vérifier les cartes du board
            for i in range(1, 6):
                region_name = f"card_{i}"
                if region_name in results:
                    regions_to_check.append(region_name)

            # Vérifier les cartes en main
            for i in range(1, 3):
                for region_format in [f"carte_{i}m", f"hand_card_{i}"]:
                    if region_format in results:
                        regions_to_check.append(region_format)

            # Supprimer les corrections pour les régions qui ont changé
            corrections_to_remove = []
            for region in self.manual_corrections:
                if region in regions_to_check:
                    # Vérifier si la région a une nouvelle valeur détectée
                    if region in results and results[region].get("text", "").strip():
                        corrections_to_remove.append(region)

            # Supprimer les corrections
            for region in corrections_to_remove:
                del self.manual_corrections[region]

            # Si des corrections ont été supprimées, vider le cache
            if corrections_to_remove:
                self.cache.clear()

    def convert_to_float(self, text):
        """
        Convertit un texte en nombre flottant en gérant les virgules et les points

        Args:
            text (str): Texte à convertir en nombre

        Returns:
            float: Valeur numérique convertie
        """
        if not text:
            return 0.0

        # Remplacer les virgules par des points pour la conversion
        text = text.replace(',', '.')

        # Supprimer les espaces et autres caractères non numériques
        # Mais garder les points décimaux
        cleaned_text = re.sub(r'[^\d.]', '', text)

        try:
            return float(cleaned_text)
        except ValueError:
            return 0.0

    def extract_poker_data(self, results):
        """
        Extrait les données de poker des résultats de détection

        Args:
            results (dict): Résultats de la détection

        Returns:
            dict: Données de poker structurées
        """
        data = {
            "hand_cards_text": "",
            "board_cards_text": "",
            "probability": 0,
            "action": "",
            "pot": 10,  # Valeurs par défaut
            "pot_total": 10,
            "effective_stack": 100,
            "my_stack": 100,
            "my_bet": 0,  # Ma mise actuelle
            "player_stacks": {},
            "player_bets": {},
            "player_allins": {},  # All-ins des joueurs
            "call_amount": 0,  # Montant à suivre
            "raise_amount": 0,  # Montant de relance
            "my_allin": 0,  # Mon all-in
            "missing_cards": [],  # Liste pour suivre les cartes manquantes
            "detected_regions": [],  # Liste des régions détectées
            "selected_regions": [],  # Liste des régions sélectionnées
            "analysis_scope": "complete"  # Portée de l'analyse
        }

        # Récupérer les métadonnées si disponibles
        metadata = results.get("_metadata", {})
        if metadata:
            data["selected_regions"] = metadata.get("selected_regions", [])
            data["total_selected"] = metadata.get("total_selected", 0)
            data["total_detected"] = metadata.get("total_detected", 0)
            print(f"🎯 Analyse complète: {data['total_detected']}/{data['total_selected']} régions détectées")

            # Analyser les types de régions sélectionnées
            selected_types = {
                "cards": 0,
                "chips": 0,
                "bets": 0,
                "players": 0,
                "other": 0
            }

            for region in data["selected_regions"]:
                if region.startswith(("card_", "carte_", "hand_card_")):
                    selected_types["cards"] += 1
                elif region.startswith(("chips_", "stack_")):
                    selected_types["chips"] += 1
                elif region.startswith(("bet_", "mise_")):
                    selected_types["bets"] += 1
                elif region.startswith("player"):
                    selected_types["players"] += 1
                else:
                    selected_types["other"] += 1

            print(f"📊 Types de régions sélectionnées: Cartes={selected_types['cards']}, Jetons={selected_types['chips']}, Mises={selected_types['bets']}, Joueurs={selected_types['players']}, Autres={selected_types['other']}")

            # Ajuster la portée de l'analyse selon les régions sélectionnées
            if selected_types["cards"] > 0 and (selected_types["chips"] > 0 or selected_types["bets"] > 0):
                data["analysis_scope"] = "complete"
            elif selected_types["cards"] > 0:
                data["analysis_scope"] = "cards_only"
            elif selected_types["chips"] > 0 or selected_types["bets"] > 0:
                data["analysis_scope"] = "money_only"
            else:
                data["analysis_scope"] = "limited"

        # Extraire les cartes du board (card_1 à card_5)
        board_cards = []
        for i in range(1, 6):
            region_name = f"card_{i}"

            # Vérifier s'il y a une correction manuelle pour cette région
            if region_name in self.manual_corrections:
                correction = self.manual_corrections[region_name]
                corrected_value = correction.get("value", "")
                corrected_suit = correction.get("suit", "")

                # Marquer cette région comme ayant une correction manuelle
                data["manual_corrections"] = data.get("manual_corrections", [])
                data["manual_corrections"].append(region_name)

                # Si la valeur est vide, c'est une correction "Pas de cartes"
                if corrected_value == "":
                    # Ne pas ajouter cette carte, passer à la suivante
                    continue

                # Si la valeur est spécifiée, traiter la correction
                if corrected_value:
                    # Si la couleur n'est pas spécifiée, essayer de la récupérer des résultats de détection
                    if not corrected_suit and region_name in results:
                        region_data = results[region_name]
                        colors = region_data.get("colors", [])
                        corrected_suit = self.determine_card_suit(colors)

                    # Si nous avons maintenant une valeur et une couleur, ajouter la carte corrigée
                    if corrected_value and corrected_suit:
                        board_cards.append(f"{corrected_value} de {corrected_suit}")
                        data["detected_regions"].append(region_name)
                        continue

            # Si pas de correction manuelle ou correction incomplète, utiliser la détection automatique
            if region_name in results:
                region_data = results[region_name]
                card_text = region_data.get("text", "").strip()
                colors = region_data.get("colors", [])

                if card_text:
                    # Corriger les erreurs de détection courantes
                    corrected_card_text = self.correct_card_value(card_text)
                    # Déterminer la couleur de la carte
                    suit = self.determine_card_suit(colors)
                    if suit and corrected_card_text in CARD_VALUES:
                        board_cards.append(f"{corrected_card_text} de {suit}")
                    data["detected_regions"].append(region_name)

        # Extraire les cartes en main (carte_1m et carte_2m ou hand_card_1 et hand_card_2)
        hand_cards = []
        hand_regions_checked = []

        # Vérifier d'abord les corrections manuelles pour les cartes en main
        for i in range(1, 3):
            # Vérifier les deux formats possibles
            region_names = [f"carte_{i}m", f"hand_card_{i}"]

            for region_name in region_names:
                if region_name in self.manual_corrections:
                    correction = self.manual_corrections[region_name]
                    corrected_value = correction.get("value", "")
                    corrected_suit = correction.get("suit", "")

                    # Marquer cette région comme ayant une correction manuelle
                    data["manual_corrections"] = data.get("manual_corrections", [])
                    data["manual_corrections"].append(region_name)
                    hand_regions_checked.append(region_name)

                    # Si la valeur est vide, c'est une correction "Pas de cartes"
                    if corrected_value == "":
                        # Ne pas ajouter cette carte, passer à la suivante
                        continue

                    # Si la valeur est spécifiée, traiter la correction
                    if corrected_value:
                        # Si la couleur n'est pas spécifiée, essayer de la récupérer des résultats de détection
                        if not corrected_suit and region_name in results:
                            region_data = results[region_name]
                            colors = region_data.get("colors", [])
                            corrected_suit = self.determine_card_suit(colors)

                        # Si nous avons maintenant une valeur et une couleur, ajouter la carte corrigée
                        if corrected_value and corrected_suit:
                            hand_cards.append(f"{corrected_value} de {corrected_suit}")
                            data["detected_regions"].append(region_name)

        # Si nous n'avons pas encore 2 cartes en main, essayer la détection automatique
        if len(hand_cards) < 2:
            # Essayer d'abord avec le format carte_Xm
            for i in range(1, 3):
                region_name = f"carte_{i}m"
                if region_name not in hand_regions_checked and region_name in results:
                    region_data = results[region_name]
                    card_text = region_data.get("text", "").strip()
                    colors = region_data.get("colors", [])

                    if card_text:
                        # Corriger les erreurs de détection courantes
                        corrected_card_text = self.correct_card_value(card_text)
                        # Déterminer la couleur de la carte
                        suit = self.determine_card_suit(colors)
                        if suit and corrected_card_text in CARD_VALUES:
                            hand_cards.append(f"{corrected_card_text} de {suit}")
                        data["detected_regions"].append(region_name)

            # Si nous n'avons toujours pas 2 cartes en main, essayer avec le format hand_card_X
            if len(hand_cards) < 2:
                for i in range(1, 3):
                    region_name = f"hand_card_{i}"
                    if region_name not in hand_regions_checked and region_name in results:
                        region_data = results[region_name]
                        card_text = region_data.get("text", "").strip()
                        colors = region_data.get("colors", [])

                        if card_text:
                            # Corriger les erreurs de détection courantes
                            corrected_card_text = self.correct_card_value(card_text)
                            # Déterminer la couleur de la carte
                            suit = self.determine_card_suit(colors)
                            if suit and corrected_card_text in CARD_VALUES:
                                hand_cards.append(f"{corrected_card_text} de {suit}")
                            data["detected_regions"].append(region_name)

        # Extraire les informations sur les jetons et les mises
        # Rechercher les régions de jetons (mes_jetons, jetons_joueurX, etc.)
        for region_name, region_data in results.items():
            # Extraire les jetons du joueur principal
            if region_name == "mes_jetons":
                # Récupérer le texte brut
                chips_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"💰 Texte brut pour {region_name}: '{chips_text}'")

                if chips_text:
                    # Convertir en nombre en gérant les virgules
                    chips_value = self.convert_to_float(chips_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💰 Valeur convertie pour {region_name}: {chips_value}")

                    if chips_value > 0:  # Vérifier que la valeur est positive
                        data["my_stack"] = chips_value
                        data["effective_stack"] = chips_value  # Par défaut, le tapis effectif est celui du joueur
                        print(f"✅ Stack du joueur mis à jour: {chips_value}")
                        data["detected_regions"].append(region_name)

            # Extraire les jetons des autres joueurs
            elif region_name.startswith("jetons_joueur"):
                # Récupérer le texte brut
                chips_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"💰 Texte brut pour {region_name}: '{chips_text}'")

                if chips_text:
                    # Convertir en nombre en gérant les virgules
                    chips_value = self.convert_to_float(chips_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💰 Valeur convertie pour {region_name}: {chips_value}")

                    if chips_value > 0:  # Vérifier que la valeur est positive
                        # Extraire le numéro du joueur
                        player_match = re.search(r'joueur(\d+)', region_name)
                        if player_match:
                            player_num = player_match.group(1)
                            data["player_stacks"][f"joueur{player_num}"] = chips_value
                            # Mettre à jour le tapis effectif (le plus petit tapis à la table)
                            data["effective_stack"] = min(data["effective_stack"], chips_value)
                            print(f"✅ Stack du joueur {player_num} mis à jour: {chips_value}")
                            data["detected_regions"].append(region_name)

            # Extraire les jetons avec l'ancien format pour compatibilité
            elif region_name.startswith("chips_") or region_name.startswith("stack_"):
                # Récupérer le texte brut
                chips_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"💰 Texte brut pour {region_name}: '{chips_text}'")

                if chips_text:
                    # Convertir en nombre en gérant les virgules
                    chips_value = self.convert_to_float(chips_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💰 Valeur convertie pour {region_name}: {chips_value}")

                    if chips_value > 0:  # Vérifier que la valeur est positive
                        # Si c'est le stack du joueur principal
                        if region_name == "chips_player" or region_name == "stack_player" or region_name == "my_stack":
                            data["my_stack"] = chips_value
                            data["effective_stack"] = chips_value  # Par défaut, le tapis effectif est celui du joueur
                            print(f"✅ Stack du joueur mis à jour: {chips_value}")
                        else:
                            # Extraire le numéro du joueur si possible
                            player_match = re.search(r'player(\d+)', region_name)
                            if player_match:
                                player_num = player_match.group(1)
                                data["player_stacks"][f"player{player_num}"] = chips_value
                                # Mettre à jour le tapis effectif (le plus petit tapis à la table)
                                data["effective_stack"] = min(data["effective_stack"], chips_value)
                                print(f"✅ Stack du joueur {player_num} mis à jour: {chips_value}")

                        data["detected_regions"].append(region_name)

            # Extraire ma mise
            elif region_name == "ma_mise":
                # Récupérer le texte brut
                bet_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"💲 Texte brut pour {region_name}: '{bet_text}'")

                if bet_text:
                    # Convertir en nombre en gérant les virgules
                    bet_value = self.convert_to_float(bet_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💲 Valeur convertie pour {region_name}: {bet_value}")

                    if bet_value > 0:  # Vérifier que la valeur est positive
                        data["my_bet"] = bet_value
                        print(f"✅ Ma mise mise à jour: {bet_value}")
                        # Ajouter au pot total
                        data["pot_total"] += bet_value
                        data["detected_regions"].append(region_name)

            # Extraire les mises des autres joueurs
            elif region_name.startswith("mise_joueur"):
                # Récupérer le texte brut
                bet_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"💲 Texte brut pour {region_name}: '{bet_text}'")

                if bet_text:
                    # Convertir en nombre en gérant les virgules
                    bet_value = self.convert_to_float(bet_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💲 Valeur convertie pour {region_name}: {bet_value}")

                    if bet_value > 0:  # Vérifier que la valeur est positive
                        # Extraire le numéro du joueur
                        player_match = re.search(r'joueur(\d+)', region_name)
                        if player_match:
                            player_num = player_match.group(1)
                            data["player_bets"][f"joueur{player_num}"] = bet_value
                            print(f"✅ Mise du joueur {player_num} mise à jour: {bet_value}")
                            # Ajouter au pot total
                            data["pot_total"] += bet_value
                            data["detected_regions"].append(region_name)

            # Extraire les mises avec l'ancien format pour compatibilité
            elif region_name.startswith("bet_") or region_name.startswith("mise_"):
                # Récupérer le texte brut
                bet_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"💲 Texte brut pour {region_name}: '{bet_text}'")

                if bet_text:
                    # Convertir en nombre en gérant les virgules
                    bet_value = self.convert_to_float(bet_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💲 Valeur convertie pour {region_name}: {bet_value}")

                    if bet_value > 0:  # Vérifier que la valeur est positive
                        # Si c'est la mise du joueur principal
                        if region_name == "bet_player" or region_name == "mise_player":
                            data["my_bet"] = bet_value
                            print(f"✅ Mise du joueur mise à jour: {bet_value}")
                        else:
                            # Extraire le numéro du joueur si possible
                            player_match = re.search(r'player(\d+)', region_name)
                            if player_match:
                                player_num = player_match.group(1)
                                data["player_bets"][f"player{player_num}"] = bet_value
                                print(f"✅ Mise du joueur {player_num} mise à jour: {bet_value}")

                        # Ajouter au pot total
                        data["pot_total"] += bet_value
                        data["detected_regions"].append(region_name)

            # Extraire le pot
            elif region_name in ["pot", "pot_size", "pot_total"]:
                # Récupérer le texte brut
                pot_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"🏆 Texte brut pour {region_name}: '{pot_text}'")

                if pot_text:
                    # Convertir en nombre en gérant les virgules
                    pot_value = self.convert_to_float(pot_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"🏆 Valeur convertie pour {region_name}: {pot_value}")

                    if pot_value > 0:  # Vérifier que la valeur est positive
                        data["pot"] = pot_value
                        data["pot_total"] = pot_value  # Mettre à jour le pot total
                        print(f"✅ Pot mis à jour: {pot_value}")
                        data["detected_regions"].append(region_name)

            # Extraire les montants de call et relance
            elif region_name in ["montant_call", "montant_relance"]:
                # Récupérer le texte brut
                amount_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"💵 Texte brut pour {region_name}: '{amount_text}'")

                if amount_text:
                    # Convertir en nombre en gérant les virgules
                    amount_value = self.convert_to_float(amount_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"💵 Valeur convertie pour {region_name}: {amount_value}")

                    if amount_value > 0:  # Vérifier que la valeur est positive
                        if region_name == "montant_call":
                            data["call_amount"] = amount_value
                            print(f"✅ Montant call mis à jour: {amount_value}")
                        elif region_name == "montant_relance":
                            data["raise_amount"] = amount_value
                            print(f"✅ Montant relance mis à jour: {amount_value}")
                        data["detected_regions"].append(region_name)

            # Extraire les all-in
            elif region_name.startswith("allin_joueur") or region_name == "mon_allin":
                # Récupérer le texte brut
                allin_text = region_data.get("text", "").strip()

                # Afficher le texte brut pour le débogage
                print(f"🔥 Texte brut pour {region_name}: '{allin_text}'")

                if allin_text:
                    # Convertir en nombre en gérant les virgules
                    allin_value = self.convert_to_float(allin_text)

                    # Afficher la valeur convertie pour le débogage
                    print(f"🔥 Valeur convertie pour {region_name}: {allin_value}")

                    if allin_value > 0:  # Vérifier que la valeur est positive
                        if region_name == "mon_allin":
                            data["my_allin"] = allin_value
                            print(f"✅ Mon all-in mis à jour: {allin_value}")
                        else:
                            # Extraire le numéro du joueur
                            player_match = re.search(r'joueur(\d+)', region_name)
                            if player_match:
                                player_num = player_match.group(1)
                                data["player_allins"][f"joueur{player_num}"] = allin_value
                                print(f"✅ All-in du joueur {player_num} mis à jour: {allin_value}")
                        data["detected_regions"].append(region_name)

        # Mettre à jour les données
        data["board_cards_text"] = ", ".join(board_cards) if board_cards else "Non détectées"
        data["hand_cards_text"] = ", ".join(hand_cards) if hand_cards else "Non détectées"

        # Vérifier les cartes en double
        all_cards = board_cards + hand_cards
        duplicates = [card for card in all_cards if all_cards.count(card) > 1]
        if duplicates:
            unique_duplicates = list(set(duplicates))
            data["missing_cards"].append(f"duplicate_cards:{','.join(unique_duplicates)}")

        return data

    def correct_card_value(self, card_text):
        """
        Corrige les erreurs de détection courantes dans les valeurs de cartes

        Args:
            card_text (str): Texte de la carte détectée

        Returns:
            str: Texte corrigé de la carte
        """
        # Nettoyer le texte (supprimer les espaces et caractères spéciaux)
        cleaned_text = card_text.strip()

        # Si le texte est déjà une valeur valide, le retourner tel quel
        if cleaned_text in CARD_VALUES:
            return cleaned_text

        # Cas spécial pour distinguer 9 et 6 (problème fréquent)
        if cleaned_text.lower() in ["g", "q"]:
            return "9"  # Si c'est un g ou q, c'est probablement un 9
        if cleaned_text.lower() in ["b", "p"]:
            return "6"  # Si c'est un b ou p, c'est probablement un 6

        # Vérifier si le texte est dans notre dictionnaire de corrections
        if cleaned_text in CARD_VALUE_CORRECTIONS:
            return CARD_VALUE_CORRECTIONS[cleaned_text]

        # Si le texte est un seul caractère, vérifier les corrections
        if len(cleaned_text) == 1 and cleaned_text in CARD_VALUE_CORRECTIONS:
            return CARD_VALUE_CORRECTIONS[cleaned_text]

        # Cas spécial pour le 10 qui peut être détecté comme IO, l0, etc.
        if cleaned_text.lower() in ["io", "l0", "lo", "to", "t0"]:
            return "10"

        # Cas spécial pour les figures
        if cleaned_text.lower() in ["as", "a", "ace"]:
            return "A"
        if cleaned_text.lower() in ["roi", "r", "k", "king"]:
            return "K"
        if cleaned_text.lower() in ["dame", "d", "q", "queen"]:
            return "Q"
        if cleaned_text.lower() in ["valet", "v", "j", "jack"]:
            return "J"

        # Cas spécial pour les chiffres écrits en toutes lettres
        number_words = {
            "one": "1", "un": "1", "une": "1",
            "two": "2", "deux": "2",
            "three": "3", "trois": "3",
            "four": "4", "quatre": "4",
            "five": "5", "cinq": "5",
            "six": "6", "six": "6",
            "seven": "7", "sept": "7",
            "eight": "8", "huit": "8",
            "nine": "9", "neuf": "9",
            "ten": "10", "dix": "10"
        }
        if cleaned_text.lower() in number_words:
            return number_words[cleaned_text.lower()]

        # Analyse contextuelle pour 9 vs 6 (si le texte contient ces caractères)
        if "g" in cleaned_text.lower() or "q" in cleaned_text.lower():
            return "9"
        if "b" in cleaned_text.lower() or "p" in cleaned_text.lower():
            return "6"

        # Si aucune correction n'est trouvée, retourner le texte original
        return cleaned_text

    def determine_card_suit(self, colors):
        """
        Détermine la couleur d'une carte à partir des couleurs détectées

        Args:
            colors (list): Liste des couleurs détectées

        Returns:
            str: Couleur de la carte (Cœur, Pique, Trèfle, Carreau) ou None
        """
        if not colors:
            return None

        # Correspondance entre les couleurs détectées et les couleurs de cartes
        if "red" in colors:
            return "Cœur"
        elif "black" in colors and "white" not in colors:
            return "Pique"
        elif "green" in colors:
            return "Trèfle"
        elif "blue" in colors:
            return "Carreau"

        # Par défaut, retourner Pique si on ne peut pas déterminer
        return "Pique"

    def analyze_poker_situation(self, data):
        """
        Analyse la situation de poker

        Args:
            data (dict): Données de poker extraites

        Returns:
            dict: Résultats de l'analyse
        """
        results = {
            "hand_cards": [],
            "board_cards": [],
            "hand_strength": "",
            "equity": (0, 0),
            "pot_odds": 0,
            "bet_to_call": 0,  # Montant à suivre
            "implied_odds": "N/A",
            "recommended_action": "",
            "action_reason": "",
            "notes": [],
            "detected_regions": data.get("detected_regions", []),
            "detected_chips": False,  # Indicateur pour les jetons détectés
            "detected_bets": False,   # Indicateur pour les mises détectées
            "detected_pot": False     # Indicateur pour le pot détecté
        }

        # Vérifier si les jetons, mises et pot ont été détectés
        for region in data.get("detected_regions", []):
            # Vérifier les jetons (nouveaux et anciens formats)
            if (region == "mes_jetons" or region.startswith("jetons_joueur") or
                region.startswith("chips_") or region.startswith("stack_")):
                results["detected_chips"] = True
            # Vérifier les mises (nouveaux et anciens formats)
            elif (region == "ma_mise" or region.startswith("mise_joueur") or
                  region.startswith("bet_") or region.startswith("mise_")):
                results["detected_bets"] = True
            # Vérifier le pot (nouveaux et anciens formats)
            elif region in ["pot", "pot_size", "pot_total"]:
                results["detected_pot"] = True

        # Analyser les cartes
        hand_cards = self.parse_cards(data["hand_cards_text"])
        board_cards = self.parse_cards(data["board_cards_text"])

        # Convertir au format Texas Hold'em
        results["hand_cards"] = self.cards_to_texas_format(hand_cards)
        results["board_cards"] = self.cards_to_texas_format(board_cards)

        # Ajouter des notes pour les cartes manquantes
        for issue in data["missing_cards"]:
            if issue.startswith("duplicate_cards:"):
                cards = issue.split(":")[1].split(",")
                results["notes"].append(f"⚠️ Cartes en double détectées: {', '.join(cards)}")

        # Évaluer la force de la main seulement si nous avons des cartes valides
        if results["hand_cards"] or results["board_cards"]:
            results["hand_strength"] = self.evaluate_hand_strength(results["board_cards"], results["hand_cards"])

            # Estimer l'équité
            results["equity"] = self.estimate_equity(results["board_cards"], results["hand_cards"])

            # Ajouter une note spécifique pour les mains très faibles
            if not results["board_cards"] and len(results["hand_cards"]) == 2:
                # Vérifier si c'est une main extrêmement faible
                category, description, _ = self.evaluate_preflop_hand(results["hand_cards"])
                if category == 9 and "À COUCHER" in description:
                    results["notes"].append("⛔ MAIN TRÈS FAIBLE: VOUS DEVRIEZ VOUS COUCHER ⛔")
        else:
            results["hand_strength"] = "Non détecté"
            results["equity"] = (0, 0)
            results["notes"].append("⚠️ Impossible d'évaluer la main: aucune carte valide détectée")

        # Calculer les pot odds en fonction des données réelles si disponibles
        bet_to_call = 0
        for player_bet in data["player_bets"].values():
            bet_to_call = max(bet_to_call, player_bet)

        # Stocker le montant à suivre dans les résultats
        results["bet_to_call"] = bet_to_call

        # Si nous avons une mise à suivre et un pot
        if bet_to_call > 0 and data["pot"] > 0:
            pot_odds = (bet_to_call / (data["pot"] + bet_to_call)) * 100
            results["pot_odds"] = round(pot_odds, 1)
        else:
            # Valeur par défaut si nous n'avons pas d'informations sur les mises
            results["pot_odds"] = 20

        # Calculer le rapport stack-to-pot
        if data["pot"] > 0 and data["effective_stack"] > 0:
            stack_to_pot_ratio = data["effective_stack"] / data["pot"]
        else:
            stack_to_pot_ratio = 10  # Valeur par défaut

        # Calculer les cotes implicites
        if data["pot"] > 0 and data["my_stack"] > 0:
            implied_odds = (data["pot"] + sum(data["player_stacks"].values())) / data["my_stack"]
            results["implied_odds"] = f"{implied_odds:.1f}:1"

        # Ajouter des informations sur les jetons détectés
        if data["my_stack"] != 100:  # Si ce n'est pas la valeur par défaut
            results["notes"].append(f"💰 Vos jetons: {data['my_stack']}")

        if data["player_stacks"]:
            stack_info = ", ".join([f"Joueur {k[-1:]}: {v}" for k, v in data["player_stacks"].items()])
            results["notes"].append(f"💰 Jetons adverses: {stack_info}")

        if data["pot"] != 10:  # Si ce n'est pas la valeur par défaut
            results["notes"].append(f"🏆 Pot: {data['pot']}")

        # Ajouter une note sur le montant à suivre si nécessaire
        if results["bet_to_call"] > 0:
            results["notes"].append(f"💲 Mise à suivre: {results['bet_to_call']:.0f} BB")

        # Recommander une action en fonction des informations disponibles
        if results["hand_cards"]:
            # Si nous avons des cartes en main, nous pouvons donner une recommandation
            action, reason = self.recommend_action(
                results["equity"],
                results["pot_odds"],
                stack_to_pot_ratio,
                data["my_stack"],
                data["pot"],
                results["bet_to_call"]  # Passer le montant à suivre
            )
            results["recommended_action"] = action
            results["action_reason"] = reason
        else:
            # Si nous n'avons pas de cartes en main, recommander d'attendre
            results["recommended_action"] = "attendre"
            results["action_reason"] = "Cartes en main non détectées"
            results["notes"].append("⚠️ Recommandation impossible: cartes en main non détectées")

        # Ajouter une note sur les régions détectées
        if results["detected_regions"]:
            results["notes"].append(f"🔍 Régions détectées: {len(results['detected_regions'])}")

        # Ajouter une note sur les corrections manuelles
        if "manual_corrections" in data and data["manual_corrections"]:
            corrections_list = ", ".join(data["manual_corrections"])
            results["notes"].append(f"✏️ Corrections manuelles: {len(data['manual_corrections'])} ({corrections_list})")

        return results

    def parse_cards(self, card_text):
        """
        Analyse le texte des cartes et retourne une liste de tuples (valeur, couleur)

        Args:
            card_text (str): Texte contenant les cartes (ex: "As de Cœur, Roi de Pique")

        Returns:
            list: Liste de tuples (valeur, couleur)
        """
        if not card_text or card_text == "Non détectées":
            return []

        cards = []
        # Diviser le texte en cartes individuelles
        card_parts = card_text.split(", ")

        for part in card_parts:
            # Rechercher la valeur et la couleur
            match = re.search(r"([A-Z0-9]+) de (Cœur|Pique|Trèfle|Carreau)", part)
            if match:
                value, suit = match.groups()
                cards.append((value, suit))

        return cards

    def cards_to_texas_format(self, cards):
        """
        Convertit les cartes au format Texas Hold'em (ex: As♥)

        Args:
            cards (list): Liste de tuples (valeur, couleur)

        Returns:
            list: Liste de cartes au format Texas Hold'em
        """
        if not cards:
            return []

        # Symboles des couleurs
        suit_symbols = {
            "Cœur": "♥",
            "Pique": "♠",
            "Trèfle": "♣",
            "Carreau": "♦"
        }

        # Conversion des valeurs
        value_conversion = {
            "A": "A", "K": "K", "Q": "Q", "J": "J", "10": "T",
            "9": "9", "8": "8", "7": "7", "6": "6", "5": "5",
            "4": "4", "3": "3", "2": "2"
        }

        texas_cards = []
        for value, suit in cards:
            if value in value_conversion and suit in suit_symbols:
                texas_cards.append(f"{value_conversion[value]}{suit_symbols[suit]}")

        return texas_cards

    def evaluate_preflop_hand(self, hand_cards):
        """
        Évalue la force d'une main pre-flop

        Args:
            hand_cards (list): Liste des cartes en main au format Texas Hold'em

        Returns:
            tuple: (catégorie, description, rang)
                catégorie: 1-9 (1=premium, 9=très faible)
                description: Description textuelle de la main
                rang: Valeur numérique pour comparer les mains (plus élevé = meilleur)
        """
        if len(hand_cards) != 2:
            return (9, "Main incomplète", 0)

        # Extraire les valeurs et les couleurs
        values = [card[0] for card in hand_cards]
        suits = [card[1] for card in hand_cards]

        # Convertir les valeurs en valeurs numériques pour la comparaison
        value_map = {"A": 14, "K": 13, "Q": 12, "J": 11, "T": 10,
                     "9": 9, "8": 8, "7": 7, "6": 6, "5": 5, "4": 4, "3": 3, "2": 2}
        numeric_values = [value_map.get(v, 0) for v in values]
        numeric_values.sort(reverse=True)  # Trier par ordre décroissant

        # Vérifier si les cartes sont de la même couleur (suited)
        suited = suits[0] == suits[1]

        # Vérifier si c'est une paire
        is_pair = numeric_values[0] == numeric_values[1]

        # Vérifier si les cartes sont connectées (consécutives)
        connected = abs(numeric_values[0] - numeric_values[1]) == 1

        # Vérifier si les cartes sont presque connectées (écart de 2)
        almost_connected = abs(numeric_values[0] - numeric_values[1]) == 2

        # Calculer l'écart entre les cartes
        gap = abs(numeric_values[0] - numeric_values[1])

        # Calculer le rang de la main (plus élevé = meilleur)
        hand_rank = 0

        # Classification des mains pre-flop
        if is_pair:
            # Paires
            if numeric_values[0] >= 10:  # AA, KK, QQ, JJ, TT
                category = 1
                description = f"Paire premium ({values[0]}{values[1]})"
                hand_rank = 90 + numeric_values[0]
            elif numeric_values[0] >= 7:  # 99, 88, 77
                category = 2
                description = f"Paire moyenne ({values[0]}{values[1]})"
                hand_rank = 80 + numeric_values[0]
            else:  # 66, 55, 44, 33, 22
                category = 4
                description = f"Petite paire ({values[0]}{values[1]})"
                hand_rank = 70 + numeric_values[0]
        elif numeric_values[0] >= 13 and numeric_values[1] >= 11:  # AK, AQ, KQ
            # Cartes hautes premium
            if suited:
                category = 1
                description = f"Main premium assortie ({values[0]}{values[1]}s)"
                hand_rank = 85 + numeric_values[0] + numeric_values[1] * 0.1
            else:
                category = 2
                description = f"Main premium non assortie ({values[0]}{values[1]}o)"
                hand_rank = 75 + numeric_values[0] + numeric_values[1] * 0.1
        elif numeric_values[0] >= 13 and numeric_values[1] >= 9:  # AJ, AT, A9, KJ, KT
            # Cartes hautes fortes
            if suited:
                category = 2
                description = f"Main forte assortie ({values[0]}{values[1]}s)"
                hand_rank = 65 + numeric_values[0] + numeric_values[1] * 0.1
            else:
                category = 3
                description = f"Main forte non assortie ({values[0]}{values[1]}o)"
                hand_rank = 55 + numeric_values[0] + numeric_values[1] * 0.1
        elif numeric_values[0] >= 13:  # Ax
            # As avec petite carte
            if suited:
                category = 4
                description = f"As avec petite carte assortie ({values[0]}{values[1]}s)"
                hand_rank = 45 + numeric_values[1] * 0.5
            else:
                category = 6
                description = f"As avec petite carte non assortie ({values[0]}{values[1]}o)"
                hand_rank = 35 + numeric_values[1] * 0.5
        elif connected and numeric_values[0] >= 10:  # Connecteurs hauts: KQ, QJ, JT
            # Connecteurs hauts
            if suited:
                category = 3
                description = f"Connecteurs hauts assortis ({values[0]}{values[1]}s)"
                hand_rank = 60 + numeric_values[0] * 0.5
            else:
                category = 4
                description = f"Connecteurs hauts non assortis ({values[0]}{values[1]}o)"
                hand_rank = 50 + numeric_values[0] * 0.5
        elif connected:  # Connecteurs moyens et bas
            # Connecteurs moyens et bas
            if suited:
                category = 4
                description = f"Connecteurs assortis ({values[0]}{values[1]}s)"
                hand_rank = 40 + numeric_values[0] * 0.5
            else:
                category = 6
                description = f"Connecteurs non assortis ({values[0]}{values[1]}o)"
                hand_rank = 30 + numeric_values[0] * 0.5
        elif almost_connected and numeric_values[0] >= 10:  # Presque connecteurs hauts: K9, Q9, J9
            # Presque connecteurs hauts
            if suited:
                category = 5
                description = f"Presque connecteurs hauts assortis ({values[0]}{values[1]}s)"
                hand_rank = 35 + numeric_values[0] * 0.5
            else:
                category = 7
                description = f"Presque connecteurs hauts non assortis ({values[0]}{values[1]}o)"
                hand_rank = 25 + numeric_values[0] * 0.5
        elif suited and numeric_values[0] >= 10:  # Cartes hautes assorties
            category = 5
            description = f"Cartes hautes assorties ({values[0]}{values[1]}s)"
            hand_rank = 30 + numeric_values[0] * 0.5 + numeric_values[1] * 0.1
        elif numeric_values[0] >= 10 and numeric_values[1] >= 8:  # Cartes hautes non assorties
            category = 6
            description = f"Cartes hautes non assorties ({values[0]}{values[1]}o)"
            hand_rank = 20 + numeric_values[0] * 0.5 + numeric_values[1] * 0.1
        elif suited and gap <= 4:  # Cartes moyennes assorties avec petit écart
            category = 7
            description = f"Cartes moyennes assorties ({values[0]}{values[1]}s)"
            hand_rank = 15 + numeric_values[0] * 0.3 + numeric_values[1] * 0.1
        # Cas spécifique pour 5-3 et autres mains faibles similaires
        elif numeric_values[0] <= 7 and numeric_values[1] <= 5 and gap >= 2:
            # Mains très faibles avec un grand écart
            if suited:
                category = 9
                description = f"Main très faible assortie ({values[0]}{values[1]}s)"
                hand_rank = 5 + numeric_values[0] * 0.1 + numeric_values[1] * 0.05
            else:
                category = 9
                description = f"Main très faible non assortie ({values[0]}{values[1]}o)"
                hand_rank = 3 + numeric_values[0] * 0.1 + numeric_values[1] * 0.05
        # Cas spécifique pour les mains extrêmement faibles (comme 5-3, 7-2, etc.)
        elif numeric_values[0] <= 7 and numeric_values[1] <= 3:
            # Mains extrêmement faibles
            if suited:
                category = 9
                description = f"Main extrêmement faible assortie ({values[0]}{values[1]}s) - À COUCHER"
                hand_rank = 2 + numeric_values[0] * 0.1 + numeric_values[1] * 0.05
            else:
                category = 9
                description = f"Main extrêmement faible non assortie ({values[0]}{values[1]}o) - À COUCHER"
                hand_rank = 1 + numeric_values[0] * 0.1 + numeric_values[1] * 0.05
        else:
            # Autres mains faibles
            if suited:
                category = 8
                description = f"Main faible assortie ({values[0]}{values[1]}s)"
                hand_rank = 10 + numeric_values[0] * 0.2 + numeric_values[1] * 0.1
            else:
                category = 9
                description = f"Main faible non assortie ({values[0]}{values[1]}o)"
                hand_rank = 5 + numeric_values[0] * 0.2 + numeric_values[1] * 0.1

        return (category, description, hand_rank)

    def evaluate_hand_strength(self, board_cards, hand_cards):
        """
        Évalue la force de la main

        Args:
            board_cards (list): Liste des cartes du board au format Texas Hold'em
            hand_cards (list): Liste des cartes en main au format Texas Hold'em

        Returns:
            str: Description de la force de la main
        """
        # Si nous sommes au pre-flop (pas de cartes sur le board), utiliser l'évaluation pre-flop
        if not board_cards and len(hand_cards) == 2:
            _, description, _ = self.evaluate_preflop_hand(hand_cards)
            return description

        # Sinon, évaluer la main avec toutes les cartes
        all_cards = board_cards + hand_cards

        # Vérifier les paires
        values = [card[0] for card in all_cards]
        value_counts = {value: values.count(value) for value in set(values)}

        # Vérifier les couleurs
        suits = [card[1] for card in all_cards]
        suit_counts = {suit: suits.count(suit) for suit in set(suits)}

        # Déterminer la force de la main
        if any(count >= 4 for count in value_counts.values()):
            return "Carré"
        elif any(count >= 3 for count in value_counts.values()) and any(count >= 2 for count in value_counts.values()):
            return "Full House"
        elif any(count >= 5 for count in suit_counts.values()):
            return "Couleur"
        elif len(all_cards) >= 5:
            return "Quinte possible"
        elif any(count >= 3 for count in value_counts.values()):
            return "Brelan"
        elif list(value_counts.values()).count(2) >= 2:
            return "Deux paires"
        elif any(count >= 2 for count in value_counts.values()):
            return "Paire"
        else:
            return "Hauteur"

    def estimate_equity(self, board_cards, hand_cards):
        """
        Estime l'équité de la main contre une range adverse

        Args:
            board_cards (list): Liste des cartes du board au format Texas Hold'em
            hand_cards (list): Liste des cartes en main au format Texas Hold'em

        Returns:
            tuple: (min_equity, max_equity) en pourcentage
        """
        # Si nous sommes au pre-flop, utiliser l'évaluation pre-flop
        if not board_cards and len(hand_cards) == 2:
            category, _, hand_rank = self.evaluate_preflop_hand(hand_cards)

            # Estimer l'équité en fonction de la catégorie de la main pre-flop
            if category == 1:  # Mains premium
                return (65, 85)  # AA, KK, QQ, AKs, etc.
            elif category == 2:  # Mains fortes
                return (55, 70)  # JJ, TT, AQo, AJs, etc.
            elif category == 3:  # Mains solides
                return (50, 65)  # 99, AJo, KQs, etc.
            elif category == 4:  # Mains jouables
                return (45, 60)  # 88, 77, ATs, KJs, etc.
            elif category == 5:  # Mains spéculatives fortes
                return (40, 55)  # 66, A9s, KTs, QJs, etc.
            elif category == 6:  # Mains spéculatives
                return (35, 50)  # 55, A8s, KTo, etc.
            elif category == 7:  # Mains marginales
                return (30, 45)  # 44, A7s, K9s, etc.
            elif category == 8:  # Mains faibles
                return (25, 40)  # 33, A5s, K8s, etc.
            elif category == 9:  # Mains très faibles
                # Vérifier si c'est une main particulièrement faible (comme 5-3)
                if hand_rank < 10:  # Mains avec un rang très bas
                    return (10, 25)  # 5-3, 7-2, etc. (mains très faibles)
                else:
                    return (15, 30)  # 22, A4s, K7s, etc. (mains faibles standard)

        # Sinon, évaluer la main avec toutes les cartes
        hand_strength = self.evaluate_hand_strength(board_cards, hand_cards)

        if hand_strength == "Carré":
            return (85, 95)
        elif hand_strength == "Full House":
            return (80, 90)
        elif hand_strength == "Couleur":
            return (75, 85)
        elif hand_strength == "Quinte possible":
            return (60, 75)
        elif hand_strength == "Brelan":
            return (65, 80)
        elif hand_strength == "Deux paires":
            return (55, 70)
        elif hand_strength == "Paire":
            return (40, 60)
        else:
            return (20, 40)

    def recommend_action(self, equity, pot_odds, stack_to_pot_ratio, my_stack=100, pot=10, bet_to_call=0):
        """
        Recommande une action optimale

        Args:
            equity (tuple): (min_equity, max_equity) en pourcentage
            pot_odds (float): Pot odds en pourcentage
            stack_to_pot_ratio (float): Rapport stack-to-pot
            my_stack (float): Nombre de jetons du joueur
            pot (float): Taille du pot
            bet_to_call (float): Montant à suivre

        Returns:
            tuple: (action, raison)
        """
        min_equity, max_equity = equity
        avg_equity = (min_equity + max_equity) / 2

        # Ajuster les recommandations en fonction du nombre de jetons
        short_stack = my_stack < 20  # Tapis court
        medium_stack = 20 <= my_stack < 50  # Tapis moyen
        deep_stack = my_stack >= 50  # Tapis profond

        # Vérifier si nous sommes au pre-flop (en utilisant l'équité comme indicateur)
        is_preflop = False
        # Plage d'équité élargie pour inclure les mains très faibles
        if 10 <= min_equity <= 65 and 25 <= max_equity <= 85:
            is_preflop = True

        # Recommandations spécifiques pour le pre-flop
        if is_preflop:
            # Catégoriser la main en fonction de l'équité
            if avg_equity >= 75:  # Mains premium (AA, KK)
                if bet_to_call == 0:  # Personne n'a misé
                    if short_stack:
                        return ("all-in", "Main premium avec un tapis court, maximiser la valeur.")
                    elif medium_stack:
                        return ("raise 3BB", "Main premium, relance standard pour construire le pot.")
                    else:
                        return ("raise 3BB", "Main premium, relance standard pour construire le pot.")
                elif bet_to_call <= 3:  # Petite mise à suivre
                    if short_stack:
                        return ("all-in", "Main premium avec un tapis court, maximiser la valeur.")
                    elif medium_stack:
                        return ("raise 3x", "Main premium, relance pour isoler et construire le pot.")
                    else:
                        return ("raise 3x", "Main premium, relance pour isoler et construire le pot.")
                else:  # Grosse mise à suivre
                    if short_stack:
                        return ("all-in", "Main premium avec un tapis court, maximiser la valeur.")
                    elif bet_to_call > my_stack * 0.2:
                        return ("all-in", "Main premium face à une grosse mise, maximiser la valeur.")
                    else:
                        return ("raise 2.5x", "Main premium, relance pour isoler et construire le pot.")

            elif avg_equity >= 60:  # Mains fortes (QQ, JJ, AK)
                if bet_to_call == 0:  # Personne n'a misé
                    if short_stack:
                        return ("all-in", "Main forte avec un tapis court, maximiser la valeur.")
                    elif medium_stack:
                        return ("raise 2.5BB", "Main forte, relance standard pour construire le pot.")
                    else:
                        return ("raise 2.5BB", "Main forte, relance standard pour construire le pot.")
                elif bet_to_call <= 3:  # Petite mise à suivre
                    if short_stack:
                        return ("all-in", "Main forte avec un tapis court, maximiser la valeur.")
                    elif medium_stack:
                        return ("raise 2.5x", "Main forte, relance pour isoler et construire le pot.")
                    else:
                        return ("raise 2.5x", "Main forte, relance pour isoler et construire le pot.")
                else:  # Grosse mise à suivre
                    if short_stack:
                        return ("all-in", "Main forte avec un tapis court, maximiser la valeur.")
                    elif bet_to_call > my_stack * 0.25:
                        return (f"call {bet_to_call:.0f} BB", "Main forte, suivre pour voir le flop.")
                    else:
                        return ("raise 2x", "Main forte, relance pour isoler et construire le pot.")

            elif avg_equity >= 50:  # Mains solides (TT, 99, AQ, AJs)
                if bet_to_call == 0:  # Personne n'a misé
                    if short_stack:
                        return ("all-in", "Main solide avec un tapis court, maximiser la valeur.")
                    elif medium_stack:
                        return ("raise 2.5BB", "Main solide, relance standard pour construire le pot.")
                    else:
                        return ("raise 2.5BB", "Main solide, relance standard pour construire le pot.")
                elif bet_to_call <= 3:  # Petite mise à suivre
                    if short_stack:
                        return ("all-in", "Main solide avec un tapis court, maximiser la valeur.")
                    elif medium_stack:
                        return ("raise 2.5x", "Main solide, relance pour isoler et construire le pot.")
                    else:
                        return ("raise 2.5x", "Main solide, relance pour isoler et construire le pot.")
                else:  # Grosse mise à suivre
                    if short_stack:
                        return ("all-in", "Main solide avec un tapis court, maximiser la valeur.")
                    elif bet_to_call > my_stack * 0.3:
                        return ("fold", "Main solide mais trop coûteux pour suivre.")
                    else:
                        return (f"call {bet_to_call:.0f} BB", "Main solide, suivre pour voir le flop.")

            elif avg_equity >= 42:  # Mains jouables (88, 77, AJo, KQs)
                if bet_to_call == 0:  # Personne n'a misé
                    if short_stack:
                        return ("all-in", "Main jouable avec un tapis court, prendre l'initiative.")
                    elif medium_stack:
                        return ("raise 2BB", "Main jouable, petite relance pour prendre l'initiative.")
                    else:
                        return ("raise 2BB", "Main jouable, petite relance pour prendre l'initiative.")
                elif bet_to_call <= 2:  # Très petite mise à suivre
                    if short_stack:
                        return ("all-in", "Main jouable avec un tapis court, prendre l'initiative.")
                    else:
                        return (f"call {bet_to_call:.0f} BB", "Main jouable, suivre pour voir le flop.")
                else:  # Mise à suivre plus importante
                    if short_stack and bet_to_call <= my_stack * 0.2:
                        return ("all-in", "Main jouable avec un tapis court, dernière chance.")
                    elif bet_to_call > my_stack * 0.15:
                        return ("fold", "Main jouable mais trop coûteux pour suivre.")
                    else:
                        return (f"call {bet_to_call:.0f} BB", "Main jouable, suivre pour voir le flop.")

            elif avg_equity >= 35:  # Mains spéculatives (66, 55, A9s, KTs)
                if bet_to_call == 0:  # Personne n'a misé
                    if short_stack:
                        return ("all-in", "Main spéculative avec un tapis court, prendre l'initiative.")
                    elif deep_stack:
                        return ("raise 2BB", "Main spéculative, petite relance en position tardive.")
                    else:
                        return ("limp", "Main spéculative, entrer doucement dans le pot.")
                elif bet_to_call <= 2:  # Très petite mise à suivre
                    if short_stack:
                        return ("all-in", "Main spéculative avec un tapis court, prendre l'initiative.")
                    elif deep_stack and pot_odds < avg_equity:
                        return (f"call {bet_to_call:.0f} BB", "Main spéculative, suivre pour voir le flop.")
                    else:
                        return ("fold", "Main spéculative, se coucher face à une relance.")
                else:  # Mise à suivre plus importante
                    if short_stack and bet_to_call <= my_stack * 0.15:
                        return ("all-in", "Main spéculative avec un tapis court, dernière chance.")
                    else:
                        return ("fold", "Main spéculative, se coucher face à une relance importante.")

            elif avg_equity >= 25:  # Mains faibles mais jouables
                if bet_to_call == 0:  # Personne n'a misé
                    if short_stack:
                        return ("all-in", "Main faible avec un tapis court, dernière chance.")
                    elif deep_stack:
                        return ("limp", "Main faible, entrer doucement dans le pot en position tardive.")
                    else:
                        return ("fold", "Main faible, se coucher.")
                else:  # Mise à suivre
                    if short_stack and bet_to_call <= my_stack * 0.1:
                        return ("all-in", "Main faible avec un tapis court, dernière chance.")
                    else:
                        return ("fold", "Main faible, se coucher face à une mise.")
            else:  # Mains extrêmement faibles (comme 5-3, 7-2)
                if bet_to_call == 0:  # Personne n'a misé
                    if short_stack and avg_equity > 15:  # Même avec un tapis court, être plus sélectif
                        return ("all-in", "Main très faible avec un tapis court, stratégie désespérée.")
                    else:
                        return ("fold", "Main extrêmement faible, se coucher immédiatement.")
                else:  # Mise à suivre
                    return ("fold", "Main extrêmement faible, se coucher face à toute mise.")

        # Recommandations pour le post-flop (code existant)
        else:
            # Décider de l'action en fonction de l'équité, des pot odds et du nombre de jetons
            if avg_equity < 25:
                if short_stack and avg_equity > 15:
                    return ("all-in", "Tapis court avec une main faible mais jouable.")
                return ("fold", "Main faible avec peu d'équité contre la range adverse.")

            if pot_odds > 0 and avg_equity > pot_odds:
                # Cas où l'équité est supérieure aux pot odds (rentable de suivre)
                if avg_equity > 75:
                    # Main très forte
                    if short_stack:
                        return ("all-in", "Main très forte avec un tapis court, maximiser la valeur.")
                    elif medium_stack and stack_to_pot_ratio < 3:
                        return ("all-in", "Main très forte avec un tapis moyen, maximiser la valeur.")
                    elif deep_stack:
                        if pot > my_stack * 0.3:
                            return ("value-bet 3/4 pot", "Main très forte avec un gros pot, extraire de la valeur.")
                        else:
                            return ("value-bet 2/3 pot", "Main très forte, construire le pot.")
                    else:
                        return ("value-bet 3/4 pot", "Main très forte, extraire de la valeur.")
                elif avg_equity > 60:
                    # Bonne main
                    if short_stack:
                        return ("all-in", "Bonne main avec un tapis court.")
                    elif medium_stack:
                        if pot > my_stack * 0.2:
                            return ("value-bet 1/2 pot", "Bonne main avec un tapis moyen, construire le pot.")
                        else:
                            return (f"call {bet_to_call:.0f} BB", "Bonne main, contrôler le pot avec un tapis moyen.")
                    else:
                        return ("value-bet 1/2 pot", "Bonne main avec équité favorable contre la range adverse.")
                else:
                    # Main moyenne
                    if short_stack and avg_equity > 40:
                        return ("all-in", "Main moyenne avec un tapis court.")
                    elif medium_stack and avg_equity > 50:
                        return (f"call {bet_to_call:.0f} BB", "Main moyenne avec un tapis moyen, voir le prochain tour.")
                    else:
                        return (f"call {bet_to_call:.0f} BB", "Équité suffisante par rapport aux cotes du pot.")
            elif pot_odds == 0:
                # Cas où il n'y a pas de mise à suivre (check possible)
                if avg_equity > 60:
                    if short_stack:
                        return ("all-in", "Bonne main avec un tapis court, prendre l'initiative.")
                    elif medium_stack:
                        if pot > my_stack * 0.15:
                            return ("value-bet 1/2 pot", "Bonne main avec un tapis moyen, prendre l'initiative.")
                        else:
                            return ("value-bet 1/3 pot", "Bonne main, mise de sondage avec un tapis moyen.")
                    else:
                        return ("value-bet 2/3 pot", "Bonne main, prendre l'initiative.")
                elif avg_equity > 40:
                    if short_stack:
                        return ("all-in", "Main moyenne avec un tapis court.")
                    else:
                        return ("check", "Main moyenne, contrôler le pot.")
                else:
                    return ("check", "Main faible, voir le prochain tour gratuitement.")
            else:
                # Cas où l'équité est inférieure aux pot odds (non rentable de suivre)
                if avg_equity > 50 and stack_to_pot_ratio < 5:
                    if short_stack:
                        return ("all-in", "Équité marginale mais tapis court.")
                    else:
                        return (f"call {bet_to_call:.0f} BB", "Équité marginale mais cote implicite favorable.")
                elif short_stack and avg_equity > 35:
                    return ("all-in", "Tapis court, dernière chance avec une main jouable.")
                else:
                    return ("fold", "Équité insuffisante par rapport aux cotes du pot.")

    def format_analysis(self, data, analysis):
        """
        Formate l'analyse pour l'affichage

        Args:
            data (dict): Données de poker
            analysis (dict): Résultats de l'analyse

        Returns:
            str: Analyse formatée
        """
        # Formater les cartes du board
        board_str = "Non détectées"
        if analysis["board_cards"]:
            board_str = " ".join(analysis["board_cards"])

        # Formater les cartes en main
        hand_str = "Non détectées"
        if analysis["hand_cards"]:
            hand_str = " ".join(analysis["hand_cards"])

        # Indicateurs visuels pour les cartes
        board_indicator = "🟢 " if analysis["board_cards"] else "🔴 "
        hand_indicator = "🟢 " if analysis["hand_cards"] else "🔴 "

        # Indicateurs visuels pour les mises et les jetons
        pot_indicator = "🟢 " if analysis.get("detected_pot", False) else "🔴 "
        my_stack_indicator = "🟢 " if analysis.get("detected_chips", False) else "🔴 "
        player_stacks_indicator = "🟢 " if analysis.get("detected_chips", False) else "🔴 "
        bets_indicator = "🟢 " if analysis.get("detected_bets", False) else "🔴 "

        # Formater l'équité
        min_equity, max_equity = analysis["equity"]
        avg_equity = (min_equity + max_equity) / 2
        equity_str = f"{min_equity}-{max_equity}" if min_equity != max_equity else f"{min_equity}"

        # Formater les pot odds
        pot_odds_str = f"{analysis['pot_odds']:.1f}" if analysis['pot_odds'] > 0 else "N/A"

        # Formater l'action recommandée
        action_str = analysis["recommended_action"].upper() if analysis["recommended_action"] else "ATTENDRE"

        # Ajouter un indicateur visuel pour les recommandations "fold"
        if "fold" in action_str.lower():
            action_str = f"⛔ SE COUCHER ⛔"

        # Ajouter une note sur le montant à suivre si nécessaire
        elif analysis["bet_to_call"] > 0 and "call" in action_str.lower() and "BB" not in action_str:
            action_str = f"{action_str} {analysis['bet_to_call']:.0f} BB"

        # Formater les informations sur les jetons
        stack_info = f"{data['my_stack']:.0f}" if data['my_stack'] != 100 else "100"

        # Déterminer la catégorie de tapis
        stack_category = ""
        if data['my_stack'] < 20:
            stack_category = " (Tapis court ⚠️)"
        elif data['my_stack'] < 50:
            stack_category = " (Tapis moyen)"
        elif data['my_stack'] >= 50:
            stack_category = " (Tapis profond 💰)"

        # Les informations sur les adversaires et les mises sont maintenant directement dans le template

        # Horodatage
        timestamp = time.strftime("%H:%M:%S")

        # Déterminer le nombre de régions détectées
        regions_count = len(analysis.get("detected_regions", []))

        # Déterminer le nombre de corrections manuelles
        manual_corrections_count = len(data.get("manual_corrections", []))

        # Créer l'info des régions
        regions_info = f"({regions_count} régions détectées"
        if manual_corrections_count > 0:
            regions_info += f", {manual_corrections_count} corrections manuelles"
        regions_info += ")" if regions_count > 0 or manual_corrections_count > 0 else ""

        # Construire le template
        template = f"""### Situation {regions_info}
Board : {board_indicator}{board_str}
Main : {hand_indicator}{hand_str}
Pot : {pot_indicator}{data['pot']:.0f} BB   Pot total : {data['pot_total']:.0f} BB
Tapis : {my_stack_indicator}{stack_info} BB{stack_category}
{player_stacks_indicator}Adversaires : {', '.join([f"J{k[-1:]}: {v:.0f}" for k, v in data["player_stacks"].items()]) if data["player_stacks"] else "Non détectés"}
{bets_indicator}Mises : {', '.join([f"J{k[-1:]}: {v:.0f}" for k, v in data["player_bets"].items()]) if data["player_bets"] else "Non détectées"}

### Analyse rapide
- Force : {analysis['hand_strength']}
- Équité estimée vs range ({equity_str}) : ~{avg_equity:.1f}%
- Pot odds : {pot_odds_str}%   Cote implicite : {analysis['implied_odds']}
- Mise à suivre : {analysis['bet_to_call']:.0f} BB

### Recommandation
**Action optimale** : {action_str}
*Raison* : {analysis['action_reason']}
{"⚠️ **VOUS DEVRIEZ VOUS COUCHER DANS CETTE SITUATION** ⚠️" if "fold" in analysis["recommended_action"].lower() else ""}

{chr(10).join(analysis['notes']) if analysis['notes'] else ""}
Dernière mise à jour : {timestamp}"""

        return template


class PokerDataCache:
    """Classe pour gérer le cache des données et des analyses de poker."""

    def __init__(self, max_size=20):
        """
        Initialise le cache avec une taille maximale.

        Args:
            max_size (int): Nombre maximum d'entrées dans le cache
        """
        self.max_size = max_size
        self.cache = OrderedDict()  # Utiliser OrderedDict pour conserver l'ordre d'insertion
        self.hits = 0  # Nombre de fois où le cache a été utilisé
        self.misses = 0  # Nombre de fois où le cache n'a pas été utilisé
        self.total_requests = 0  # Nombre total de requêtes

    def get_hash(self, data):
        """
        Calcule un hash unique pour les données de poker.

        Args:
            data (dict): Données de poker

        Returns:
            str: Hash unique des données
        """
        # Créer une représentation stable des données
        key_parts = [
            data.get("hand_cards_text", ""),
            data.get("board_cards_text", ""),
            str(data.get("pot", 0)),
            str(data.get("pot_total", 0)),
            str(data.get("effective_stack", 0))
        ]

        # Joindre les parties et calculer un hash
        key = "|".join(key_parts)
        return key

    def get(self, data):
        """
        Récupère les résultats d'analyse du cache si disponibles.

        Args:
            data (dict): Données de poker

        Returns:
            tuple: (analysis, formatted_analysis, from_cache) ou (None, None, False) si non trouvé
        """
        self.total_requests += 1

        # Calculer le hash des données
        data_hash = self.get_hash(data)

        # Vérifier si le hash est dans le cache
        if data_hash in self.cache:
            # Déplacer l'entrée à la fin (la plus récemment utilisée)
            entry = self.cache.pop(data_hash)
            self.cache[data_hash] = entry

            self.hits += 1
            return entry["analysis"], entry["formatted_analysis"], True

        self.misses += 1
        return None, None, False

    def put(self, data, analysis, formatted_analysis):
        """
        Ajoute une entrée au cache.

        Args:
            data (dict): Données de poker
            analysis (dict): Résultats de l'analyse
            formatted_analysis (str): Analyse formatée
        """
        # Calculer le hash des données
        data_hash = self.get_hash(data)

        # Ajouter au cache
        self.cache[data_hash] = {
            "analysis": analysis,
            "formatted_analysis": formatted_analysis,
            "timestamp": time.time()
        }

        # Si le cache dépasse la taille maximale, supprimer l'entrée la plus ancienne
        if len(self.cache) > self.max_size:
            self.cache.popitem(last=False)  # Supprimer le premier élément (le plus ancien)

    def clear(self):
        """
        Vide complètement le cache.

        Returns:
            int: Nombre d'entrées supprimées
        """
        count = len(self.cache)
        self.cache.clear()
        return count
