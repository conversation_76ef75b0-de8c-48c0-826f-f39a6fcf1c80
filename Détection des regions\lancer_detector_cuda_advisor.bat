@echo off
echo ===================================================
echo Lancement de Detector GUI avec CUDA et Conseiller Poker
echo ===================================================
echo.

echo Verification de CUDA...
python -c "import torch; print(f'CUDA disponible: {torch.cuda.is_available()}')"
if %ERRORLEVEL% NEQ 0 (
    echo PyTorch n'est pas installe ou CUDA n'est pas disponible.
    echo Veuillez executer install_paddle_cuda.bat pour installer les dependances.
    pause
    exit /b 1
)

echo.
echo Verification de PaddleOCR avec CUDA...
python -c "import paddle; print(f'PaddlePaddle compile avec CUDA: {paddle.device.is_compiled_with_cuda()}')"
if %ERRORLEVEL% NEQ 0 (
    echo PaddlePaddle n'est pas installe.
    echo Veuillez executer install_paddle_cuda.bat pour installer les dependances.
    pause
    exit /b 1
)

echo.
echo Verification du module Conseiller Poker...
python -c "import sys; sys.path.append('C:/Users/<USER>/PokerAdvisor/Détection des regions'); from poker_advisor_light import PokerAdvisorLight; print('Module Conseiller Poker disponible')"
if %ERRORLEVEL% NEQ 0 (
    echo Le module Conseiller Poker n'est pas disponible.
    echo Veuillez verifier que le fichier poker_advisor_light.py existe.
    pause
    exit /b 1
)

echo.
echo Lancement de Detector GUI avec CUDA active et Conseiller Poker integre...
echo Avec surveillance de stabilite amelioree...
echo.

rem Définir la variable d'environnement pour activer CUDA
set USE_CUDA=1

rem Définir la variable d'environnement pour activer le conseiller poker
set USE_POKER_ADVISOR=1

rem Définir la variable d'environnement pour activer la surveillance
set ENABLE_MONITORING=1

rem Lancer l'application avec CUDA activé et conseiller poker
cd "C:\Users\<USER>\PokerAdvisor\Détection des regions"

echo.
echo ===================================================
echo AMÉLIORATIONS DE STABILITÉ APPLIQUÉES:
echo ===================================================
echo ✅ Nettoyage automatique de la mémoire GPU
echo ✅ Gestion d'erreurs consécutives améliorée
echo ✅ Arrêt automatique en cas de problème
echo ✅ Surveillance de la mémoire en temps réel
echo ===================================================
echo.

python detector_gui.py --use-cuda --use-advisor

echo.
echo Application fermee. Verification des logs...
if exist monitor.log (
    echo.
    echo === DERNIÈRES ENTRÉES DU LOG DE SURVEILLANCE ===
    powershell "Get-Content 'C:\Users\<USER>\PokerAdvisor\Détection des regions\monitor.log' | Select-Object -Last 10"
)

echo.
pause
